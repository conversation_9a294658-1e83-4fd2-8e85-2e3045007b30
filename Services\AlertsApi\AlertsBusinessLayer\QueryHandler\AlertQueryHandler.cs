using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsBusinessLayer.QueryHandler
{
    public class AlertQueryHandler : IAlertQueryHandler<Alert>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AlertQueryHandler> _logger;
        private readonly IStringLocalizer<AlertQueryHandler> _localizer;

        public AlertQueryHandler(
            IUnitOfWork unitOfWork,
            ILogger<AlertQueryHandler> logger,
            IStringLocalizer<AlertQueryHandler> localizer)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _localizer = localizer;
        }

        public async Task<IEnumerable<Alert>> GetAllAlerts()
        {
            try
            {
                _logger.LogInformation(_localizer["GettingAllAlerts"]);
                var alerts = await _unitOfWork.AlertRepository.GetAllAlertsAsync();
                _logger.LogInformation(_localizer["GetAllAlertsSuccess"]);
                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetAllAlertsError"]);
                throw new Exception(_localizer["GetAllAlertsErrorGeneric"], ex);
            }
        }

        public async Task<Alert> GetAlertById(Guid id)
        {
            try
            {
                _logger.LogInformation(_localizer["GettingAlertById"], id);
                var alert = await _unitOfWork.AlertRepository.GetByIdAsync(id);
                if (alert == null)
                {
                    _logger.LogWarning(_localizer["AlertNotFound"], id);
                    throw new KeyNotFoundException(_localizer["AlertNotFound", id]);
                }
                _logger.LogInformation(_localizer["GetAlertByIdSuccess"], id);
                return alert;
            }
            catch (KeyNotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetAlertByIdError"], id);
                throw new Exception(_localizer["GetAlertByIdErrorGeneric", id], ex);
            }
        }

        public async Task<IEnumerable<Alert>> GetAlertsByPatientId(Guid patientId)
        {
            try
            {
                _logger.LogInformation(_localizer["GettingAlertsByPatientId"], patientId);
                var alerts = await _unitOfWork.AlertRepository.GetAlertsByPatientIdAsync(patientId);
                _logger.LogInformation(_localizer["GetAlertsByPatientIdSuccess"], patientId);
                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetAlertsByPatientIdError"], patientId);
                throw new Exception(_localizer["GetAlertsByPatientIdErrorGeneric", patientId], ex);
            }
        }

        public async Task<IEnumerable<Alert>> GetActiveAlertsByPatientId(Guid patientId)
        {
            try
            {
                _logger.LogInformation(_localizer["GettingActiveAlertsByPatientId"], patientId);
                var activeAlerts = await _unitOfWork.AlertRepository.GetActiveAlertsByPatientIdAsync(patientId);
                _logger.LogInformation(_localizer["GetActiveAlertsByPatientIdSuccess"], patientId);
                return activeAlerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetActiveAlertsByPatientIdError"], patientId);
                throw new Exception(_localizer["GetActiveAlertsByPatientIdErrorGeneric", patientId], ex);
            }
        }

        public async Task<IEnumerable<Alert>> GetActiveAlertsByOrganizationId(Guid organizationId)
        {
            try
            {
                _logger.LogInformation(_localizer["GettingActiveAlertsByorganizationId"], organizationId);
                var activeAlerts = await _unitOfWork.AlertRepository.GetActiveAlertsByOrganizationIdAsync(organizationId);
                _logger.LogInformation(_localizer["GetActiveAlertsByorganizationIdSuccess"], organizationId);
                return activeAlerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetActiveAlertsByorganizationIdError"], organizationId);
                throw new Exception(_localizer["GetActiveAlertsByorganizationIdErrorGeneric", organizationId], ex);
            }
        }
    }
}
