using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using MudBlazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class DataHub : ComponentBase
    {
        [Inject] private IDataHubService DataHubService { get; set; } = default!;
        [Inject] private IStringLocalizer<DataHub> Localizer { get; set; } = default!;
        [Inject] private ILogger<DataHub> Logger { get; set; } = default!;
        [Inject] private IJSRuntime JSRuntime { get; set; } = default!;
        [Inject] private ISnackbar Snackbar { get; set; } = default!;
        [Inject] private IDialogService DialogService { get; set; } = default!;

        private PatientRegistryFilter _currentFilter = new();
        private List<PatientRegistryData>? _patientResults;
        private bool _isLoading = false;
        private bool _hasResults = false;
        private bool _searchPerformed = false;
        private Guid _organizationId = Guid.NewGuid(); // This should come from user context
        private bool _subscription = true; // This should come from user context

        protected override async Task OnInitializedAsync()
        {
            try
            {
                Logger.LogInformation(Localizer["InitializingDataHub"]);
                
                // Initialize with default filter
                _currentFilter = new PatientRegistryFilter
                {
                    PageSize = 50,
                    PageNumber = 1,
                    SortBy = "LastName",
                    SortDescending = false
                };

                await base.OnInitializedAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorInitializingDataHub"]);
                Snackbar.Add(Localizer["ErrorInitializingDataHub"], Severity.Error);
            }
        }

        private async Task RunNewQuery()
        {
            try
            {
                Logger.LogInformation(Localizer["RunningNewQuery"]);
                _isLoading = true;
                _searchPerformed = true;
                StateHasChanged();

                var queryOperation = new DataHubQueryOperation
                {
                    Id = Guid.NewGuid(),
                    Name = "New Query",
                    OperationType = QueryOperationType.RunNew,
                    Filter = _currentFilter,
                    QueryJson = System.Text.Json.JsonSerializer.Serialize(_currentFilter),
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.NewGuid(), // This should come from user context
                    OrganizationId = _organizationId
                };

                _patientResults = await DataHubService.ExecuteQueryAsync(queryOperation, _organizationId, _subscription);
                _hasResults = _patientResults?.Any() == true;

                if (_hasResults)
                {
                    Snackbar.Add(Localizer["QueryExecutedSuccessfully", _patientResults!.Count], Severity.Success);
                }
                else
                {
                    Snackbar.Add(Localizer["NoResultsFound"], Severity.Info);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorRunningQuery"]);
                Snackbar.Add(Localizer["ErrorRunningQuery"], Severity.Error);
            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        private async Task RunSubsetQuery()
        {
            try
            {
                Logger.LogInformation(Localizer["RunningSubsetQuery"]);
                _isLoading = true;
                StateHasChanged();

                var queryOperation = new DataHubQueryOperation
                {
                    Id = Guid.NewGuid(),
                    Name = "Subset Query",
                    OperationType = QueryOperationType.RunSubset,
                    Filter = _currentFilter,
                    QueryJson = System.Text.Json.JsonSerializer.Serialize(_currentFilter),
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.NewGuid(),
                    OrganizationId = _organizationId
                };

                _patientResults = await DataHubService.ExecuteQueryAsync(queryOperation, _organizationId, _subscription);
                _hasResults = _patientResults?.Any() == true;

                Snackbar.Add(Localizer["SubsetQueryExecuted"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorRunningSubsetQuery"]);
                Snackbar.Add(Localizer["ErrorRunningSubsetQuery"], Severity.Error);
            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        private async Task RunSubsetNotQuery()
        {
            try
            {
                Logger.LogInformation(Localizer["RunningSubsetNotQuery"]);
                _isLoading = true;
                StateHasChanged();

                var queryOperation = new DataHubQueryOperation
                {
                    Id = Guid.NewGuid(),
                    Name = "Subset NOT Query",
                    OperationType = QueryOperationType.RunSubsetNot,
                    Filter = _currentFilter,
                    QueryJson = System.Text.Json.JsonSerializer.Serialize(_currentFilter),
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.NewGuid(),
                    OrganizationId = _organizationId
                };

                _patientResults = await DataHubService.ExecuteQueryAsync(queryOperation, _organizationId, _subscription);
                _hasResults = _patientResults?.Any() == true;

                Snackbar.Add(Localizer["SubsetNotQueryExecuted"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorRunningSubsetNotQuery"]);
                Snackbar.Add(Localizer["ErrorRunningSubsetNotQuery"], Severity.Error);
            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        private async Task SaveQuery()
        {
            try
            {
                var parameters = new DialogParameters
                {
                    ["Filter"] = _currentFilter,
                    ["ResultCount"] = _patientResults?.Count ?? 0
                };

                var dialog = await DialogService.ShowAsync<SaveQueryDialog>(Localizer["SaveQuery"], parameters);
                var result = await dialog.Result;

                if (!result.Canceled && result.Data is SavedQuery savedQuery)
                {
                    await DataHubService.SaveQueryAsync(savedQuery);
                    Snackbar.Add(Localizer["QuerySavedSuccessfully"], Severity.Success);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSavingQuery"]);
                Snackbar.Add(Localizer["ErrorSavingQuery"], Severity.Error);
            }
        }

        private async Task ExportToText()
        {
            try
            {
                if (_patientResults == null || !_patientResults.Any())
                {
                    Snackbar.Add(Localizer["NoDataToExport"], Severity.Warning);
                    return;
                }

                Logger.LogInformation(Localizer["ExportingToText"]);
                var textContent = await DataHubService.ExportPatientRegistryToTextAsync(_patientResults);
                
                var fileName = $"PatientRegistry_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                await JSRuntime.InvokeVoidAsync("downloadFile", fileName, "text/plain", textContent);
                
                Snackbar.Add(Localizer["ExportedSuccessfully"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorExportingToText"]);
                Snackbar.Add(Localizer["ErrorExportingToText"], Severity.Error);
            }
        }

        private async Task ExportToCsv()
        {
            try
            {
                if (_patientResults == null || !_patientResults.Any())
                {
                    Snackbar.Add(Localizer["NoDataToExport"], Severity.Warning);
                    return;
                }

                Logger.LogInformation(Localizer["ExportingToCsv"]);
                var csvBytes = await DataHubService.ExportPatientRegistryToCsvAsync(_patientResults);
                
                var fileName = $"PatientRegistry_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                await JSRuntime.InvokeVoidAsync("downloadFileFromBytes", fileName, "text/csv", csvBytes);
                
                Snackbar.Add(Localizer["ExportedSuccessfully"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorExportingToCsv"]);
                Snackbar.Add(Localizer["ErrorExportingToCsv"], Severity.Error);
            }
        }

        private async Task SearchPatients()
        {
            await RunNewQuery();
        }

        private void OnFilterChanged(PatientRegistryFilter filter)
        {
            _currentFilter = filter;
            StateHasChanged();
        }

        private void OnPatientSelected(PatientRegistryData patient)
        {
            // Handle patient selection - could navigate to patient details
            Logger.LogInformation(Localizer["PatientSelected"], patient.FullName);
            Snackbar.Add(Localizer["PatientSelected", patient.FullName], Severity.Info);
        }
    }
}
