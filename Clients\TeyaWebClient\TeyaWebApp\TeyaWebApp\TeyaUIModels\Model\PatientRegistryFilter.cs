using System;
using System.Collections.Generic;

namespace TeyaUIModels.Model
{
    public class PatientRegistryFilter
    {
        public string? SearchTerm { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime? DateOfBirthFrom { get; set; }
        public DateTime? DateOfBirthTo { get; set; }
        public string? Gender { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? ZipCode { get; set; }
        public string? InsuranceProvider { get; set; }
        public string? PrimaryProvider { get; set; }
        public DateTime? LastVisitFrom { get; set; }
        public DateTime? LastVisitTo { get; set; }
        public bool? IsActive { get; set; }
        public List<string>? DiagnosisCodes { get; set; }
        public List<string>? Medications { get; set; }
        public List<string>? Allergies { get; set; }
        public string? RegistryTab { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }
}
