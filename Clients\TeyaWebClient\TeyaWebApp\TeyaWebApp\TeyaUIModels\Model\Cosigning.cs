﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Cosigning : IModel
    {
        public Guid Id { get; set; }
        public string CosignerName { get; set; }
        public string SignerName { get; set; }
        public Guid SignerId { get; set; }
        public Guid CosignerId { get; set; }
        public bool IsSigned { get; set; } = false;
        public bool IsCosigned { get; set; } = false;
        public bool IsLocked { get; set; } = false;
        public Guid RecordId { get; set; }
        public Guid OrganizationId { get; set; }
        public string Notes { get; set; }
        public DateTime? Date { get; set; }
        public DateTime? LastUpdated { get; set; }

    }
}