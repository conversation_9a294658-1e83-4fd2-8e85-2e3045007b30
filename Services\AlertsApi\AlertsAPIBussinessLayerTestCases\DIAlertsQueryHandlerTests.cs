using AlertsBusinessLayer;
using AlertsBusinessLayer.QueryHandler;
using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIBussinessLayerTestCases
{
    [TestFixture]
    public class DIAlertsQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<DIAlertsQueryHandler>> _mockLogger;
        private Mock<IStringLocalizer<DIAlertsQueryHandler>> _mockLocalizer;
        private Mock<IDIAlertsRepository> _mockDIAlertsRepository;
        private DIAlertsQueryHandler _handler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<DIAlertsQueryHandler>>();
            _mockLocalizer = new Mock<IStringLocalizer<DIAlertsQueryHandler>>();
            _mockDIAlertsRepository = new Mock<IDIAlertsRepository>();

            _mockUnitOfWork.Setup(u => u.DIAlertsRepository).Returns(_mockDIAlertsRepository.Object);

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));
            _mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()]).Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));

            _handler = new DIAlertsQueryHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllDIAlerts_ShouldReturnAllAlerts()
        {
            // Arrange
            var alerts = new List<DIAlerts>
            {
                new DIAlerts { Id = Guid.NewGuid(), Name = "DI Alert 1", Description = "Description 1", IsActive = true },
                new DIAlerts { Id = Guid.NewGuid(), Name = "DI Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockDIAlertsRepository.Setup(r => r.GetAllDIAlertsAsync()).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetAllDIAlerts();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(alerts));
            _mockDIAlertsRepository.Verify(r => r.GetAllDIAlertsAsync(), Times.Once);
        }

        [Test]
        public async Task GetAllDIAlerts_WhenExceptionOccurs_ShouldPropagateException()
        {
            // Arrange
            var exceptionMessage = "Database error";
            _mockDIAlertsRepository.Setup(r => r.GetAllDIAlertsAsync())
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetAllDIAlerts());
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }

        [Test]
        public async Task GetDIAlertsById_WithExistingId_ShouldReturnAlert()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new DIAlerts { Id = alertId, Name = "DI Alert 1", Description = "Description 1", IsActive = true };

            _mockDIAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync(alert);

            // Act
            var result = await _handler.GetDIAlertsById(alertId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(alertId));
            Assert.That(result.Name, Is.EqualTo("DI Alert 1"));
            Assert.That(result.Description, Is.EqualTo("Description 1"));
        }

        [Test]
        public void GetDIAlertsById_WithNonExistentId_ShouldThrowKeyNotFoundException()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockDIAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync((DIAlerts)null);

            // Act & Assert
            Assert.ThrowsAsync<KeyNotFoundException>(() => _handler.GetDIAlertsById(alertId));
        }

        [Test]
        public async Task GetActiveDIAlertsByOrganizationId_ShouldReturnActiveAlertsForPatient()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<DIAlerts>
            {
                new DIAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "DI Alert 1", Description = "Description 1", IsActive = true },
                new DIAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "DI Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockDIAlertsRepository.Setup(r => r.GetActiveDIAlertsByOrganizationIdAsync(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetActiveDIAlertsByOrganizationId(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.All(a => a.OrganizationId == organizationId && a.IsActive), Is.True);
        }

        [Test]
        public void GetActiveDIAlertsByOrganizationId_WhenExceptionOccurs_ShouldPropagateException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database error";

            _mockDIAlertsRepository.Setup(r => r.GetActiveDIAlertsByOrganizationIdAsync(organizationId))
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetActiveDIAlertsByOrganizationId(organizationId));
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }
    }
}
