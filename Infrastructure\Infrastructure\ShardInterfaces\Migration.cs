﻿using DotNetEnv;
using Microsoft.Azure.SqlDatabase.ElasticScale.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using Azure.Core;
using System.Linq.Expressions;

namespace Interfaces.ShardManagement
{
    public class Migration<TContext, TEntity, TLocalizer> : IMigration<TContext, TEntity, TLocalizer>
        where TContext : DbContext
        where TEntity : class
    {
        private readonly ShardMapManagerService<TContext, TLocalizer> _shardMapManagerService;
        private readonly IStringLocalizer<TLocalizer> _localizer;
        private readonly ILogger<TContext> _logger;

        const int zero = 0, one = 1, two = 2;

        public Migration(
            ShardMapManagerService<TContext, TLocalizer> shardMapManagerService,
            IStringLocalizer<TLocalizer> localizer,
            ILogger<TContext> logger)
        {
            Env.Load();
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public void CreateShardForSingleTenant(string serverName, string databaseName, byte[] newShardKey, byte[] unsubscribed, string shardMapName, Guid orgId, HashSet<Type> allowedEntityTypes)
        {
            _shardMapManagerService.AddShardIfNotExists(serverName, databaseName, newShardKey, shardMapName);
            var allKeys = _shardMapManagerService.GetAllShardKeys(shardMapName)
                .Where(key => key.Length > zero && key[key.Length - one] == two)
                .OrderBy(key => BitConverter.ToString(key))
                .ToList();

            var nextGreaterKey = allKeys.FirstOrDefault(key => CompareShardKeys(key, unsubscribed) > zero);
            if (nextGreaterKey != null)
            {
                var contextType = typeof(TContext);
                var dbSetProperties = contextType.GetProperties().Where(p =>
                    p.PropertyType.IsGenericType &&
                    p.PropertyType.GetGenericTypeDefinition() == typeof(DbSet<>));

                foreach (var dbSetProp in dbSetProperties)
                {
                    var entityType = dbSetProp.PropertyType.GetGenericArguments()[0];

                    if (!allowedEntityTypes.Contains(entityType))
                        continue;

                    var dataToMigrate = (IEnumerable<object>)this.GetType()
                                        .GetMethod(nameof(GetDataFromShardForSingleTenent))
                                        .Invoke(this, new object[] { entityType,orgId, nextGreaterKey, newShardKey, shardMapName }); ;

                    if (dataToMigrate != null && dataToMigrate.Any())
                    {
                        var newShard = _shardMapManagerService.GetShardByKey(newShardKey, shardMapName);
                        MigrateDataToNewLocation(entityType, dataToMigrate, newShard);
                        UpdateShardMapForKey(newShardKey, newShard, shardMapName);
                        RemoveEntitiesFromSourceShardForSingleTenent(entityType, nextGreaterKey, dataToMigrate, shardMapName, orgId);
                    }
                }
            }
        }
        public IEnumerable<object> GetDataFromShardForSingleTenent(Type entityType, Guid organizationId, byte[] nextGreaterKey, byte[] newShardKey, string shardMapName)
        {
            var shard = _shardMapManagerService.GetShardByKey(nextGreaterKey, shardMapName);
            var connectionString =
                $"Server={shard.Location.Server};Initial Catalog={shard.Location.Database};{Environment.GetEnvironmentVariable("ConnectionString")}";

            var optionsBuilder = new DbContextOptionsBuilder<TContext>()
                                 .UseSqlServer(connectionString);

            using var context = (TContext)Activator.CreateInstance(
                typeof(TContext), optionsBuilder.Options, _localizer, _logger);

            var setMethod = typeof(DbContext)
                .GetMethods()
                .Single(m => m.Name == "Set" && m.IsGenericMethod && m.GetParameters().Length == 0)
                .MakeGenericMethod(entityType);

            var queryable = (IQueryable)setMethod.Invoke(context, null);

            var orgProp = entityType.GetProperty("OrganizationID");
            if (orgProp == null || orgProp.PropertyType != typeof(Guid))
                return Enumerable.Empty<object>();

            var param = Expression.Parameter(entityType, "e");
            var left = Expression.Property(param, orgProp);
            var right = Expression.Constant(organizationId);
            var predicate = Expression.Equal(left, right);

            var lambdaType = typeof(Func<,>).MakeGenericType(entityType, typeof(bool));
            var lambda = Expression.Lambda(lambdaType, predicate, param);

            var whereMethod = typeof(Queryable).GetMethods()
                .First(m => m.Name == "Where" && m.GetParameters().Length == 2)
                .MakeGenericMethod(entityType);

            var filteredQueryable =
                (IQueryable)whereMethod.Invoke(null, new object[] { queryable, lambda });

            return filteredQueryable.Cast<object>().ToList();
        }
        public void CreateAndMigrateDataToNewShard(string serverName, string databaseName, byte[] newShardKey, string shardMapName, HashSet<Type> allowedEntityTypes)
        {
            _shardMapManagerService.AddShardIfNotExists(serverName, databaseName, newShardKey, shardMapName);
            var allKeys = _shardMapManagerService.GetAllShardKeys(shardMapName)
                                                .Where(key => key.Length > zero && key[key.Length - one] == two)
                                                .OrderBy(key => BitConverter.ToString(key))
                                                .ToList();

            var nextGreaterKey = allKeys.FirstOrDefault(key => CompareShardKeys(key, newShardKey) > zero);
            if (nextGreaterKey != null)
            {
                var contextType = typeof(TContext);
                var dbSetProperties = contextType.GetProperties().Where(p =>
                    p.PropertyType.IsGenericType &&
                    p.PropertyType.GetGenericTypeDefinition() == typeof(DbSet<>));

                foreach (var dbSetProp in dbSetProperties)
                {
                    var entityType = dbSetProp.PropertyType.GetGenericArguments()[0];

                    if (!allowedEntityTypes.Contains(entityType))
                        continue;

                    var method = this.GetType()
                                             .GetMethod(nameof(GetDataFromShard))
                                             .MakeGenericMethod(entityType); 

                    var dataToMigrate = (IEnumerable<object>)method.Invoke(this, new object[] { nextGreaterKey, newShardKey, shardMapName });


                    if (dataToMigrate != null && dataToMigrate.Any())
                    {
                        var newShard = _shardMapManagerService.GetShardByKey(newShardKey, shardMapName);
                        MigrateDataToNewLocation(entityType, dataToMigrate, newShard);
                        UpdateShardMapForKey(newShardKey, newShard, shardMapName);
                        RemoveEntitiesFromSourceShard(entityType, nextGreaterKey, dataToMigrate, shardMapName);
                    }
                }
            }
        }
       

        public IEnumerable<TEntity> GetDataFromShard<TEntity>(byte[] nextGreaterKey, byte[] newShardKey, string shardMapName) where TEntity : class
        {
            var shard = _shardMapManagerService.GetShardByKey(nextGreaterKey, shardMapName);
            var connectionString = $"Server={shard.Location.Server};Initial Catalog={shard.Location.Database};{Environment.GetEnvironmentVariable("ConnectionString")}";

            var optionsBuilder = new DbContextOptionsBuilder<TContext>();
            optionsBuilder.UseSqlServer(connectionString);

            using var context = (TContext)Activator.CreateInstance(typeof(TContext), optionsBuilder.Options, _localizer, _logger);

            var dbSet = context.Set<TEntity>();

            var result = dbSet
                .AsEnumerable()
                .Where(a =>
                {
                    var idProp = typeof(TEntity).GetProperty("OrganizationID");
                    if (idProp == null)
                        throw new InvalidOperationException($"TEntity {typeof(TEntity).Name} does not have an 'OrganizationID' property.");

                    var idValue = (Guid)idProp.GetValue(a);
                    var idBytes = idValue.ToByteArray();

                    // Extend by 1 byte as per original logic
                    Array.Resize(ref idBytes, idBytes.Length + 1);

                    return CompareShardKeys(idBytes, newShardKey) <= 0;
                })
                .ToList();

            return result;
        }



        public void MigrateDataToNewLocation(Type entityType, IEnumerable<object> data, Shard newShard)
        {
            var connectionString = $"Server={newShard.Location.Server};Initial Catalog={newShard.Location.Database};{Environment.GetEnvironmentVariable("ConnectionString")}";
            var optionsBuilder = new DbContextOptionsBuilder<TContext>();
            optionsBuilder.UseSqlServer(connectionString);

            using var context = (TContext)Activator.CreateInstance(typeof(TContext), optionsBuilder.Options, _localizer, _logger);
            var dbSet = typeof(DbContext)
                     .GetMethods()
                     .Single(m => m.Name == "Set" && m.IsGenericMethod && m.GetParameters().Length == 0)
                     .MakeGenericMethod(entityType)
                     .Invoke(context, null);

            var addMethod = dbSet?.GetType().GetMethod("Add");
            foreach (var item in data)
            {
                addMethod?.Invoke(dbSet, new[] { item });
            }
            context.SaveChanges();
        }

        public void RemoveEntitiesFromSourceShard(Type entityType, byte[] shardKey, IEnumerable<object> dataToMigrate, string shardMapName)
        {
            var shard = _shardMapManagerService.GetShardByKey(shardKey, shardMapName);
            var connectionString = $"Server={shard.Location.Server};Initial Catalog={shard.Location.Database};{Environment.GetEnvironmentVariable("ConnectionString")}";
            var optionsBuilder = new DbContextOptionsBuilder<TContext>();
            optionsBuilder.UseSqlServer(connectionString);

            using var context = (TContext)Activator.CreateInstance(typeof(TContext), optionsBuilder.Options, _localizer, _logger);
            var dbSet = typeof(DbContext)
                     .GetMethods()
                     .Single(m => m.Name == "Set" && m.IsGenericMethod && m.GetParameters().Length == 0)
                     .MakeGenericMethod(entityType)
                     .Invoke(context, null);

            var removeMethod = dbSet?.GetType().GetMethod("Remove");
            foreach (var item in dataToMigrate)
            {
                removeMethod?.Invoke(dbSet, new[] { item });
            }
            context.SaveChanges();
        }



        public int CompareShardKeys(byte[] array1, byte[] array2)
        {
            for (int i = zero; i < Math.Min(array1.Length, array2.Length); i++)
            {
                if (array1[i] < array2[i]) return -one;
                if (array1[i] > array2[i]) return one;
            }
            return array1.Length.CompareTo(array2.Length);
        }

        public void RemoveEntitiesFromSourceShardForSingleTenent(Type entityType, byte[] shardKey,IEnumerable<object> dataToMigrate,string shardMapName,Guid organizationId)
        {
            var shard = _shardMapManagerService.GetShardByKey(shardKey, shardMapName);
            var connectionString =
                $"Server={shard.Location.Server};Initial Catalog={shard.Location.Database};{Environment.GetEnvironmentVariable("ConnectionString")}";

            var optionsBuilder = new DbContextOptionsBuilder<TContext>()
                                  .UseSqlServer(connectionString);

            using var context = (TContext)Activator.CreateInstance(
                typeof(TContext), optionsBuilder.Options, _localizer, _logger);

            var setMethod = typeof(DbContext)
                .GetMethods()
                .Single(m => m.Name == "Set" && m.IsGenericMethod && m.GetParameters().Length == 0)
                .MakeGenericMethod(entityType);

            var dbSet = (IQueryable)setMethod.Invoke(context, null);

            var orgProp = entityType.GetProperty("OrganizationID");
            if (orgProp == null || orgProp.PropertyType != typeof(Guid))
                return;  

            var param = Expression.Parameter(entityType, "e");
            var left = Expression.Property(param, orgProp);
            var right = Expression.Constant(organizationId);
            var predicate = Expression.Equal(left, right);

            var lambdaType = typeof(Func<,>).MakeGenericType(entityType, typeof(bool));
            var lambda = Expression.Lambda(lambdaType, predicate, param);

            var whereMethod = typeof(Queryable).GetMethods()
                .First(m => m.Name == "Where" && m.GetParameters().Length == 2)
                .MakeGenericMethod(entityType);

            var filtered = (IQueryable)whereMethod.Invoke(null, new object[] { dbSet, lambda });

            var toRemove = filtered.Cast<object>().ToList();
            if (toRemove.Any())
            {
                context.RemoveRange(toRemove);
                context.SaveChanges();
            }
        }


        public void UpdateShardMapForKey(byte[] key, Shard newShard, string shardMapName)
        {
            var shardMap = _shardMapManagerService.CreateOrGetListShardMap(shardMapName);

            try
            {
                if (shardMap.TryGetMappingForKey(key, out PointMapping<byte[]> mapping))
                {
                    if (mapping.Status == MappingStatus.Online)
                    {
                        mapping = shardMap.MarkMappingOffline(mapping);
                    }

                    shardMap.DeleteMapping(mapping);
                }
                shardMap.CreatePointMapping(key, newShard);
            }
            catch (ShardManagementException ex)
            {
                throw ex;
            }
        }

    }
}