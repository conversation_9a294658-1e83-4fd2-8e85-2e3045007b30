# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.
 
# This stage is used when running from VS in fast mode (Default for Debug configuration)

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
 
USER root
 
# Install audio dependencies in the base image

RUN apt-get update && apt-get install -y \

    libportaudio2 \

    libsndfile1 \

    libasound2 \

    libasound2-dev \

    libpulse0 \

    libpulse-dev \

    alsa-utils \

    pulseaudio
 
USER app
 
WORKDIR /app
 
EXPOSE 8080

EXPOSE 8081
 
# This stage is used to build the service project

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
 
ARG BUILD_CONFIGURATION=Release
 
WORKDIR /src
 
COPY ["Clients/TeyaWebsite/TeyaWebsite/TeyaWebsite.csproj", "Clients/TeyaWebsite/TeyaWebsite/"]
 
RUN dotnet restore "./Clients/TeyaWebsite/TeyaWebsite/TeyaWebsite.csproj"
 
COPY . .
 
WORKDIR "/src/Clients/TeyaWebsite/TeyaWebsite"
 
RUN dotnet build "./TeyaWebsite.csproj" -c $BUILD_CONFIGURATION -o /app/build
 
# This stage is used to publish the service project to be copied to the final stage

FROM build AS publish
 
ARG BUILD_CONFIGURATION=Release
 
RUN dotnet publish "./TeyaWebsite.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false
 
# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)

FROM base AS final
 
WORKDIR /app
 
COPY --from=publish /app/publish .
 
ENTRYPOINT ["dotnet", "TeyaWebsite.dll"]
 
# Customize Docker containers in Visual Studio if needed

# Explore Visual Studio fast mode, and modify this Dockerfile for both debug and production builds

 
 
