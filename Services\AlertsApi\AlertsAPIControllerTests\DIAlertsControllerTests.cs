using AlertsApi.Controllers;
using AlertsBusinessLayer;
using AlertsContracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIControllerTests
{
    [TestFixture]
    public class DIAlertsControllerTests
    {
        private Mock<IDIAlertsCommandHandler<DIAlerts>> _mockDIAlertsCommandHandler;
        private Mock<IDIAlertsQueryHandler<DIAlerts>> _mockDIAlertsQueryHandler;
        private Mock<ILogger<DIAlertsController>> _mockLogger;
        private Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>> _mockLocalizer;
        private DIAlertsController _controller;

        [SetUp]
        public void Setup()
        {
            _mockDIAlertsCommandHandler = new Mock<IDIAlertsCommandHandler<DIAlerts>>();
            _mockDIAlertsQueryHandler = new Mock<IDIAlertsQueryHandler<DIAlerts>>();
            _mockLogger = new Mock<ILogger<DIAlertsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));
            _mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()]).Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));

            _controller = new DIAlertsController(
                _mockDIAlertsCommandHandler.Object,
                _mockDIAlertsQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }


        [Test]
        public async Task GetAllById_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var Id = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockDIAlertsQueryHandler.Setup(h => h.GetDIAlertsById(Id)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllById(Id);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsOkResult_WithActiveAlerts()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<DIAlerts>
            {
                new DIAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "DI Alert 1", Description = "Description 1", IsActive = true },
                new DIAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "DI Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockDIAlertsQueryHandler.Setup(h => h.GetActiveDIAlertsByOrganizationId(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.Value, Is.EqualTo(alerts));
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockDIAlertsQueryHandler.Setup(h => h.GetActiveDIAlertsByOrganizationId(organizationId)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllByIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddDIAlert_ReturnsOkResult_WhenAlertsAdded()
        {
            // Arrange
            var alerts = new List<DIAlerts>
            {
                new DIAlerts { Id = Guid.NewGuid(), Name = "DI Alert 1", Description = "Description 1" },
                new DIAlerts { Id = Guid.NewGuid(), Name = "DI Alert 2", Description = "Description 2" }
            };

            _mockDIAlertsCommandHandler.Setup(h => h.AddDIAlerts(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddDIAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task AddDIAlert_ReturnsBadRequest_WhenNoAlertsProvided()
        {
            // Act
            var result = await _controller.AddDIAlert(new List<DIAlerts>());

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddDIAlert_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alerts = new List<DIAlerts>
            {
                new DIAlerts { Id = Guid.NewGuid(), Name = "DI Alert 1", Description = "Description 1" }
            };

            var exceptionMessage = "Database error";
            _mockDIAlertsCommandHandler.Setup(h => h.AddDIAlerts(alerts)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.AddDIAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateDIAlert_ReturnsOkResult_WhenAlertUpdated()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new DIAlerts { Id = alertId, Name = "Updated DI Alert", Description = "Updated Description" };

            _mockDIAlertsCommandHandler.Setup(h => h.UpdateDIAlerts(alert)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateDIAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task UpdateDIAlert_ReturnsBadRequest_WhenAlertIdMismatch()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new DIAlerts { Id = Guid.NewGuid(), Name = "Updated DI Alert", Description = "Updated Description" };

            // Act
            var result = await _controller.UpdateDIAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateDIAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new DIAlerts { Id = alertId, Name = "Updated DI Alert", Description = "Updated Description" };

            _mockDIAlertsCommandHandler.Setup(h => h.UpdateDIAlerts(alert)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateDIAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task UpdateDIAlertList_ReturnsOkResult_WhenAlertsUpdated()
        {
            // Arrange
            var alerts = new List<DIAlerts>
            {
                new DIAlerts { Id = Guid.NewGuid(), Name = "Updated DI Alert 1", Description = "Updated Description 1" },
                new DIAlerts { Id = Guid.NewGuid(), Name = "Updated DI Alert 2", Description = "Updated Description 2" }
            };

            _mockDIAlertsCommandHandler.Setup(h => h.UpdateDIAlertsList(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateDIAlertList(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task UpdateDIAlertList_ReturnsBadRequest_WhenNoAlertsProvided()
        {
            // Act
            var result = await _controller.UpdateDIAlertList(new List<DIAlerts>());

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task DeleteDIAlertByEntity_ReturnsOkResult_WhenAlertDeleted()
        {
            // Arrange
            var alert = new DIAlerts { Id = Guid.NewGuid(), Name = "DI Alert to delete", Description = "Description" };

            _mockDIAlertsCommandHandler.Setup(h => h.DeleteDIAlertsByEntity(alert)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteDIAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteDIAlertByEntity_ReturnsBadRequest_WhenAlertIsNull()
        {
            // Act
            var result = await _controller.DeleteDIAlertByEntity(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task DeleteDIAlertByEntity_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alert = new DIAlerts { Id = Guid.NewGuid(), Name = "DI Alert to delete", Description = "Description" };

            _mockDIAlertsCommandHandler.Setup(h => h.DeleteDIAlertsByEntity(alert)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteDIAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteDIAlert_ReturnsOkResult_WhenAlertDeleted()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockDIAlertsCommandHandler.Setup(h => h.DeleteDIAlertsById(alertId)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteDIAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteDIAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockDIAlertsCommandHandler.Setup(h => h.DeleteDIAlertsById(alertId)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteDIAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteDIAlert_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var exceptionMessage = "Database error";

            _mockDIAlertsCommandHandler.Setup(h => h.DeleteDIAlertsById(alertId)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.DeleteDIAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }
    }
}
