﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class CosigningService : ICosigningService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _encounterNotesUrl;
        private readonly ITokenService _tokenService;

        public CosigningService(
            HttpClient httpClient,
            IConfiguration configuration,
            IStringLocalizer<TeyaUIViewModelsStrings> localizer,
            ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _tokenService = tokenService;
            _encounterNotesUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
        }

        public async Task<IEnumerable<Cosigning>> GetCosigningsByRecordId(Guid recordId)
        {
            var apiUrl = $"{_encounterNotesUrl}/api/Cosigning/RecordId/{recordId}";
            var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<IEnumerable<Cosigning>>();
        }

        public async Task AddCosigning(List<Cosigning> cosignings)
        {
            var apiUrl = $"{_encounterNotesUrl}/api/Cosigning/add";
            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = new StringContent(JsonSerializer.Serialize(cosignings), Encoding.UTF8, "application/json")
            };
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdateCosigning(Cosigning cosigning)
        {
            var apiUrl = $"{_encounterNotesUrl}/api/Cosigning/update/{cosigning.Id}";
            var request = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = new StringContent(JsonSerializer.Serialize(cosigning), Encoding.UTF8, "application/json")
            };
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();
        }

        public async Task<Cosigning> GetCosigningStatus(Guid recordId)
        {
            var apiUrl = $"{_encounterNotesUrl}/api/Cosigning/status/{recordId}";
            var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<Cosigning>();
        }



    }
}