﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaWebApp", "TeyaWebApp\TeyaWebApp\TeyaWebApp.csproj", "{3926CA72-5F7A-43C5-9069-F569D339165A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaUIModels", "TeyaWebApp\TeyaWebApp\TeyaUIModels\TeyaUIModels.csproj", "{869E3C48-40AF-4661-B9AF-1F96D800D9E0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaUIViewModels", "TeyaWebApp\TeyaWebApp\TeyaUIViewModels\TeyaUIViewModels.csproj", "{45414D44-9192-4680-9CE4-B8D326189FAD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{0788FC53-5AFE-4BD5-BAEB-82483CE8206F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TeyaWebClientUIViewModelTestCases", "Testcases\TeyaWebClientUIViewModelTestCases\TeyaWebClientUIViewModelTestCases.csproj", "{4BF17E7C-487E-4EAC-8C2C-148F051BE4AB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TeyaWebClientViewModelTestCases", "Testcases\TeyaWebClientViewModelTestCases\TeyaWebClientViewModelTestCases.csproj", "{E32146CE-431C-48F5-8488-1B8C33F2083F}"

EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3926CA72-5F7A-43C5-9069-F569D339165A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3926CA72-5F7A-43C5-9069-F569D339165A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3926CA72-5F7A-43C5-9069-F569D339165A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3926CA72-5F7A-43C5-9069-F569D339165A}.Release|Any CPU.Build.0 = Release|Any CPU
		{869E3C48-40AF-4661-B9AF-1F96D800D9E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{869E3C48-40AF-4661-B9AF-1F96D800D9E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{869E3C48-40AF-4661-B9AF-1F96D800D9E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{869E3C48-40AF-4661-B9AF-1F96D800D9E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{45414D44-9192-4680-9CE4-B8D326189FAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{45414D44-9192-4680-9CE4-B8D326189FAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{45414D44-9192-4680-9CE4-B8D326189FAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{45414D44-9192-4680-9CE4-B8D326189FAD}.Release|Any CPU.Build.0 = Release|Any CPU

		{4BF17E7C-487E-4EAC-8C2C-148F051BE4AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4BF17E7C-487E-4EAC-8C2C-148F051BE4AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4BF17E7C-487E-4EAC-8C2C-148F051BE4AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4BF17E7C-487E-4EAC-8C2C-148F051BE4AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{E32146CE-431C-48F5-8488-1B8C33F2083F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E32146CE-431C-48F5-8488-1B8C33F2083F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E32146CE-431C-48F5-8488-1B8C33F2083F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E32146CE-431C-48F5-8488-1B8C33F2083F}.Release|Any CPU.Build.0 = Release|Any CPU

	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution

		{4BF17E7C-487E-4EAC-8C2C-148F051BE4AB} = {0788FC53-5AFE-4BD5-BAEB-82483CE8206F}
		{E32146CE-431C-48F5-8488-1B8C33F2083F} = {0788FC53-5AFE-4BD5-BAEB-82483CE8206F}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {96B068F3-8390-4A8B-BFD1-AB3614BDC67D}
	EndGlobalSection
EndGlobal
