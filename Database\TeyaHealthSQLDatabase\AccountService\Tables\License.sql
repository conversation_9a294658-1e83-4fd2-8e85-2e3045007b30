﻿CREATE TABLE [AccountService].[License] (
    [Id]             UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [PlanId]         UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId] UNIQUEIDENTIFIER NOT NULL,
    [ProductId]      UNIQUEIDENTIFIER NULL,
    [Seats]          INT              NOT NULL,
    [CreatedDate]    DATETIME         DEFAULT (getdate()) NOT NULL,
    [UpdatedDate]    DATETIME         NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NOT NULL,
    [UpdatedBy]      UNIQUEIDENTIFIER NULL,
    [Status]         BIT              DEFAULT ((1)) NOT NULL,
    [ExpiryDate]     DATETIME         NOT NULL,
    [ActiveUsers]    INT              NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC),
    FOREIGN KEY ([PlanId]) REFERENCES [AccountService].[PlanTypes] ([Id]),
    CONSTRAINT [UQ_License_Organization] UNIQUE NONCLUSTERED ([OrganizationId] ASC)
);

