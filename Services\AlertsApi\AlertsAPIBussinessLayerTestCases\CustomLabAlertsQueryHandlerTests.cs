using AlertsBusinessLayer.QueryFolder;
using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIBussinessLayerTestCases
{
    [TestFixture]
    public class CustomLabAlertsQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<CustomLabAlertsQueryHandler>> _mockLogger;
        private Mock<IStringLocalizer<CustomLabAlertsQueryHandler>> _mockLocalizer;
        private Mock<ICustomLabAlertsRepository> _mockCustomLabAlertsRepository;
        private CustomLabAlertsQueryHandler _handler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<CustomLabAlertsQueryHandler>>();
            _mockLocalizer = new Mock<IStringLocalizer<CustomLabAlertsQueryHandler>>();
            _mockCustomLabAlertsRepository = new Mock<ICustomLabAlertsRepository>();

            _mockUnitOfWork.Setup(u => u.CustomLabAlertsRepository).Returns(_mockCustomLabAlertsRepository.Object);

            _handler = new CustomLabAlertsQueryHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllCustomLabAlerts_ShouldReturnAllAlerts()
        {
            // Arrange
            var alerts = new List<CustomLabAlerts>
            {
                new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Lab Alert 1", Description = "Description 1" },
                new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Lab Alert 2", Description = "Description 2" }
            };

            _mockCustomLabAlertsRepository.Setup(r => r.GetAllCustomLabAlertsAsync()).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetAllCustomLabAlerts();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(alerts));
        }

        [Test]
        public async Task GetCustomLabAlertsById_WithExistingId_ShouldReturnAlert()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomLabAlerts { Id = alertId, Name = "Lab Alert 1", Description = "Description 1" };

            _mockCustomLabAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync(alert);

            // Act
            var result = await _handler.GetCustomLabAlertsById(alertId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(alertId));
            Assert.That(result.Name, Is.EqualTo("Lab Alert 1"));
            Assert.That(result.Description, Is.EqualTo("Description 1"));
        }

        [Test]
        public void GetCustomLabAlertsById_WithNonExistentId_ShouldThrowKeyNotFoundException()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomLabAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync((CustomLabAlerts)null);

            // Act & Assert
            Assert.ThrowsAsync<KeyNotFoundException>(() => _handler.GetCustomLabAlertsById(alertId));
        }

        [Test]
        public async Task GetCustomLabAlertsById_ShouldReturnAlert()
        {
            // Arrange
            var Id = Guid.NewGuid();
            var alert = new CustomLabAlerts { Id = Id, Name = "Lab Alert 1", Description = "Description 1" };

            _mockCustomLabAlertsRepository
                .Setup(r => r.GetByIdAsync(Id))
                .ReturnsAsync(alert);

            // Act
            var result = await _handler.GetCustomLabAlertsById(Id);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(Id));
        }


        [Test]
        public async Task GetActiveCustomLabAlertsByOrganizationId_ShouldReturnActiveAlertsForPatient()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<CustomLabAlerts>
            {
                new CustomLabAlerts { Id = Guid.NewGuid(),OrganizationId=organizationId, Name = "Lab Alert 1", Description = "Description 1", IsActive = true },
                new CustomLabAlerts { Id = Guid.NewGuid(),OrganizationId=organizationId, Name = "Lab Alert 2", Description = "Description 2", IsActive = true }

            };

            _mockCustomLabAlertsRepository.Setup(r => r.GetActiveCustomLabAlertsByOrganizationIdAsync(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetActiveCustomLabAlertsByOrganizationId(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.All(a => a.OrganizationId == organizationId && a.IsActive), Is.True);
        }

        

        [Test]
        public void GetActiveCustomLabAlertsByPatientId_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database connection error";

            _mockCustomLabAlertsRepository.Setup(r => r.GetActiveCustomLabAlertsByOrganizationIdAsync(organizationId))
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetActiveCustomLabAlertsByOrganizationId(organizationId));
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }
    }
}
