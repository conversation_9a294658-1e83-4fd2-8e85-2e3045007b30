using AlertsBusinessLayer;
using AlertsBusinessLayer.QueryHandler;
using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIBussinessLayerTestCases
{
    [TestFixture]
    public class DXAlertsQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<DXAlertsQueryHandler>> _mockLogger;
        private Mock<IStringLocalizer<DXAlertsQueryHandler>> _mockLocalizer;
        private Mock<IDXAlertsRepository> _mockDXAlertsRepository;
        private DXAlertsQueryHandler _handler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<DXAlertsQueryHandler>>();
            _mockLocalizer = new Mock<IStringLocalizer<DXAlertsQueryHandler>>();
            _mockDXAlertsRepository = new Mock<IDXAlertsRepository>();

            _mockUnitOfWork.Setup(u => u.DXAlertsRepository).Returns(_mockDXAlertsRepository.Object);

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));
            _mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()]).Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));

            _handler = new DXAlertsQueryHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllDXAlerts_ShouldReturnAllAlerts()
        {
            // Arrange
            var alerts = new List<DXAlerts>
            {
                new DXAlerts { Id = Guid.NewGuid(), Name = "DX Alert 1", Description = "Description 1", IsActive = true },
                new DXAlerts { Id = Guid.NewGuid(), Name = "DX Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockDXAlertsRepository.Setup(r => r.GetAllDXAlertsAsync()).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetAllDXAlerts();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(alerts));
            _mockDXAlertsRepository.Verify(r => r.GetAllDXAlertsAsync(), Times.Once);
        }

        [Test]
        public async Task GetAllDXAlerts_WhenExceptionOccurs_ShouldPropagateException()
        {
            // Arrange
            var exceptionMessage = "Database error";
            _mockDXAlertsRepository.Setup(r => r.GetAllDXAlertsAsync())
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetAllDXAlerts());
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }

        [Test]
        public async Task GetDXAlertsById_WithExistingId_ShouldReturnAlert()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new DXAlerts { Id = alertId, Name = "DX Alert 1", Description = "Description 1", IsActive = true };

            _mockDXAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync(alert);

            // Act
            var result = await _handler.GetDXAlertsById(alertId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(alertId));
            Assert.That(result.Name, Is.EqualTo("DX Alert 1"));
            Assert.That(result.Description, Is.EqualTo("Description 1"));
        }

        [Test]
        public void GetDXAlertsById_WithNonExistentId_ShouldThrowKeyNotFoundException()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockDXAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync((DXAlerts)null);

            // Act & Assert
            Assert.ThrowsAsync<KeyNotFoundException>(() => _handler.GetDXAlertsById(alertId));
        }


        [Test]
        public async Task GetActiveDXAlertsByPatientId_ShouldReturnActiveAlertsForPatient()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<DXAlerts>
            {
                new DXAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "DX Alert 1", Description = "Description 1", IsActive = true },
                new DXAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "DX Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockDXAlertsRepository.Setup(r => r.GetActiveDXAlertsByOrganizationIdAsync(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetActiveDXAlertsByOrganizationId(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.All(a => a.OrganizationId == organizationId && a.IsActive), Is.True);
        }

        [Test]
        public void GetActiveDXAlertsByOrganizationId_WhenExceptionOccurs_ShouldPropagateException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database error";

            _mockDXAlertsRepository.Setup(r => r.GetActiveDXAlertsByOrganizationIdAsync(organizationId))
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetActiveDXAlertsByOrganizationId(organizationId));
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }
    }
}
