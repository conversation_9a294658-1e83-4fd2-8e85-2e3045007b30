using System;
using System.Collections.Generic;

namespace TeyaUIModels.Model
{
    public class PatientRegistryData
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public int? Age => DateOfBirth.HasValue ? DateTime.Now.Year - DateOfBirth.Value.Year : null;
        public string Gender { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public string InsuranceProvider { get; set; } = string.Empty;
        public string PrimaryProvider { get; set; } = string.Empty;
        public DateTime? LastVisitDate { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsActive { get; set; } = true;
        public string RoleName { get; set; } = string.Empty;
        public Guid? OrganizationID { get; set; }
        public string OrganizationName { get; set; } = string.Empty;
        public List<string> DiagnosisCodes { get; set; } = new();
        public List<string> Medications { get; set; } = new();
        public List<string> Allergies { get; set; } = new();
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }
}
