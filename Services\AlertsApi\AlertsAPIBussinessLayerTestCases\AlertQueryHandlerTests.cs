﻿using AlertsBusinessLayer.CommandHandler;
using AlertsBusinessLayer.QueryHandler;
using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AlertsAPIBussinessLayerTestCases
{
    [TestFixture]
    public class AlertQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<AlertQueryHandler>> _mockLogger;
        private Mock<IStringLocalizer<AlertQueryHandler>> _mockLocalizer;
        private Mock<IAlertRepository> _mockAlertRepository;
        private AlertQueryHandler _handler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<AlertQueryHandler>>();
            _mockLocalizer = new Mock<IStringLocalizer<AlertQueryHandler>>();
            _mockAlertRepository = new Mock<IAlertRepository>();

            _mockUnitOfWork.Setup(u => u.AlertRepository).Returns(_mockAlertRepository.Object);

            _handler = new AlertQueryHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllAlerts_ShouldReturnAlerts()
        {
            // Arrange  
            var alerts = new List<Alert>
            {
                new Alert { AlertId = Guid.NewGuid(), PatientName = "John Doe" }
            };

            _mockAlertRepository.Setup(r => r.GetAllAlertsAsync()).ReturnsAsync(alerts);
            _mockLocalizer.Setup(x => x[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            // Act  
            var result = await _handler.GetAllAlerts();

            // Assert  
            Assert.That(result, Is.EqualTo(alerts));
        }

        [Test]
        public void GetAllAlerts_ShouldThrowException_OnFailure()
        {
            // Arrange  
            _mockAlertRepository.Setup(r => r.GetAllAlertsAsync()).ThrowsAsync(new Exception("DB Error"));
            _mockLocalizer.Setup(x => x[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            // Act & Assert  
            var ex = Assert.ThrowsAsync<Exception>(() => _handler.GetAllAlerts());
            Assert.That(ex.Message, Does.Contain("GetAllAlertsErrorGeneric"));
        }

        [Test]
        public async Task GetAlertById_ShouldReturnAlert_WhenFound()
        {
            // Arrange  
            var alertId = Guid.NewGuid();
            var alert = new Alert { AlertId = alertId, PatientName = "Test" };

            _mockAlertRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync(alert);
            _mockLocalizer.Setup(x => x[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns((string key, object[] args) => new LocalizedString(key, key));

            // Act  
            var result = await _handler.GetAlertById(alertId);

            // Assert  
            Assert.That(result, Is.EqualTo(alert));
        }

        [Test]
        public void GetAlertById_ShouldThrowKeyNotFound_WhenNotFound()
        {
            // Arrange  
            var alertId = Guid.NewGuid();
            _mockAlertRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync((Alert)null);
            _mockLocalizer.Setup(x => x[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns((string key, object[] args) => new LocalizedString(key, key));

            // Act & Assert  
            var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _handler.GetAlertById(alertId));
            Assert.That(ex.Message, Does.Contain("AlertNotFound"));
        }

        [Test]
        public void GetAlertById_ShouldThrowGenericException_OnFailure()
        {
            // Arrange  
            var alertId = Guid.NewGuid();
            _mockAlertRepository.Setup(r => r.GetByIdAsync(alertId)).ThrowsAsync(new Exception("DB error"));
            _mockLocalizer.Setup(x => x[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns((string key, object[] args) => new LocalizedString(key, key));

            // Act & Assert  
            var ex = Assert.ThrowsAsync<Exception>(() => _handler.GetAlertById(alertId));
            Assert.That(ex.Message, Does.Contain("GetAlertByIdErrorGeneric"));
        }

        [Test]
        public async Task GetActiveAlertsByOrganizationId_ShouldReturnActiveAlerts()
        {
            // Arrange  
            var orgId = Guid.NewGuid();
            var alerts = new List<Alert> { new Alert { AlertId = Guid.NewGuid(), IsActive = true } };

            _mockAlertRepository.Setup(r => r.GetActiveAlertsByOrganizationIdAsync(orgId)).ReturnsAsync(alerts);
            _mockLocalizer.Setup(x => x[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns((string key, object[] args) => new LocalizedString(key, key));

            // Act  
            var result = await _handler.GetActiveAlertsByOrganizationId(orgId);

            // Assert  
            Assert.That(result, Is.EqualTo(alerts));
        }

        [Test]
        public void GetActiveAlertsByOrganizationId_ShouldThrowGenericException_OnFailure()
        {
            // Arrange  
            var orgId = Guid.NewGuid();
            _mockAlertRepository.Setup(r => r.GetActiveAlertsByOrganizationIdAsync(orgId)).ThrowsAsync(new Exception("DB Error"));
            _mockLocalizer.Setup(x => x[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns((string key, object[] args) => new LocalizedString(key, key));

            // Act & Assert  
            var ex = Assert.ThrowsAsync<Exception>(() => _handler.GetActiveAlertsByOrganizationId(orgId));
            Assert.That(ex.Message, Does.Contain("GetActiveAlertsByorganizationIdErrorGeneric"));
        }
    }
}
