﻿using Syncfusion.Blazor.Popups;
using System.Text.Json;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using System.Threading;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Syncfusion.Blazor;
using System.Text;
using System.Security.Cryptography.Xml;
using Syncfusion.Blazor.Inputs;
using Microsoft.Graph.Models;
using Microsoft.CognitiveServices.Speech.Transcription;
using static Azure.Core.HttpHeader;
using System;
using static TeyaWebApp.Components.Pages.Appointments;
using TeyaUIModels.ViewModel;


namespace TeyaWebApp.Components.Pages
{
    public partial class CosigningPage
    {

        private MudDialog _dialog;
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ILogger<Cosigning> _logger { get; set; }
        [Inject] private IStringLocalizer<Cosigning> _localizer { get; set; }
        [Inject] private PatientService _PatientService { get; set; }

        [Inject] public IProgressNotesService ProgressNotesService { get; set; }
        [Inject] private ICosigningService CosigningService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }

        [Inject] public IAssessmentsService _AssessmentsService { get; set; }

        [Parameter] public Guid RecordId { get; set; }
        public bool Dense_Radio { get; set; } = true;
        public bool IsSigned { get; set; } = false;
        private Record records;
        private string patientNotes;
        public string patient { get; set; }
        public string pcpName { get; set; }
        public string pcpId { get; set; }
        private Guid patientId { get; set; }
        private Guid OrgID { get; set; }
        private bool flag { get; set; } = false;
        [Inject] private ActiveUser _ActiveUser { get; set; }
        private SfGrid<Patient> PatientGrid;
        private List<Member> users = new List<Member>();
        private List<string> ProviderList = new List<string>();

        private Member _selectedToProvider;
        private string richTextContent = string.Empty;
        private bool Subscription = false;

        private TeyaUIModels.Model.Cosigning _existingCosigning;
        private bool _isLocked;
        private bool _hasChanges;
        private string notesBody = string.Empty;
        private string signedText = string.Empty;
        private string cosignedText = string.Empty;
        private string lockedText = string.Empty;


        protected override async Task OnInitializedAsync()
        {
            try
            {
                OrgID = await OrganizationService.GetOrganizationIdByNameAsync(_ActiveUser.OrganizationName);
                ProviderList = await MemberService.GetProviderlistAsync(OrgID, Subscription);
                if (_PatientService.PatientData != null)
                {
                    patient = _PatientService.PatientData.Name;
                    patientId = _PatientService.PatientData.Id;
                }
                pcpName = $"{_ActiveUser.givenName} {_ActiveUser.surname}";
                pcpId = _ActiveUser.id;

                await LoadAICardNotes();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during initialization");
                Snackbar.Add(_localizer["UnexpectedInitializationError"], Severity.Error);
                patientNotes = "Unexpected error loading data";
            }
        }



        private async Task LoadAICardNotes()
        {
            try
            {
                patientNotes = string.Empty;
                var cosignings = await CosigningService.GetCosigningsByRecordId(RecordId);
                _existingCosigning = cosignings.FirstOrDefault();

                if (_existingCosigning != null)
                {
                    _isLocked = _existingCosigning.IsLocked;

                    var allLines = _existingCosigning.Notes.Split('\n') ?? Array.Empty<string>();
                    notesBody = string.Join("\n", allLines.Where(line =>
                        !line.StartsWith("Electronically signed by") &&
                        !line.StartsWith("Electronically co-signed") &&
                        !line.StartsWith("Notes Locked!!")));

                    if (_existingCosigning.IsSigned)
                        signedText = $"Electronically signed by {_existingCosigning.SignerName} on {_existingCosigning.LastUpdated:MM/dd/yyyy}\n";

                    if (_existingCosigning.IsCosigned)
                        cosignedText = $"Electronically co-signed {_existingCosigning.CosignerName} on {_existingCosigning.LastUpdated:MM/dd/yyyy}\n";
                }
                else
                {
                    records = await ProgressNotesService.GetRecordByRecordIdAsync(RecordId, OrgID, Subscription);
                    var notesDictionary = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(records.Notes);
                    var formattedNotes = new StringBuilder();

                    foreach (var section in notesDictionary)
                    {
                        formattedNotes.AppendLine($"{section.Key}:");
                        foreach (var field in section.Value)
                        {
                            formattedNotes.AppendLine($"  {field.Key}: {field.Value}");
                        }
                        formattedNotes.AppendLine();
                    }

                    notesBody = formattedNotes.ToString();
                }

                patientNotes = notesBody + signedText + cosignedText;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading notes");
                Snackbar.Add(_localizer["AICardLoadError"], Severity.Error);
                patientNotes = "Error loading notes";
            }
        }




        private async Task OpenNewDialogBox()
        {
            await _dialog.ShowAsync();
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(_localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private async Task CloseNewDialogBox()
        {

            await _dialog.CloseAsync();
        }

        private async Task CancelData()
        {
            try
            {
                await InvokeAsync(StateHasChanged);
                await CloseNewDialogBox();

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error canceling operation");
                Snackbar.Add(_localizer["An error occurred"], Severity.Error);
            }
        }






        private async Task SaveData()
        {
            try
            {
                if (_existingCosigning == null && Dense_Radio)
                {
                    Snackbar.Add(Localizer["Sign before co-signing"], Severity.Error);
                    return;
                }

                var now = DateTime.Now;
                var isCosign = Dense_Radio;

                if (_existingCosigning == null)
                {
                    _existingCosigning = new TeyaUIModels.Model.Cosigning
                    {
                        Id = Guid.NewGuid(),
                        RecordId = RecordId,
                        Notes = patientNotes,
                        IsSigned = true,
                        IsCosigned = false,
                        IsLocked = false,
                        SignerId = Guid.Parse(_ActiveUser.id),
                        CosignerName = string.Empty,
                        CosignerId = Guid.Parse(_ActiveUser.id),
                        OrganizationId = (Guid)OrgID,
                        LastUpdated = now,
                        SignerName = pcpName,
                        Date = now,
                    };

                    await CosigningService.AddCosigning(new List<TeyaUIModels.Model.Cosigning> { _existingCosigning });
                }
                else
                {
                    _existingCosigning.Notes = patientNotes;

                    if (isCosign)
                    {
                        if (!_existingCosigning.IsSigned)
                        {
                            Snackbar.Add(Localizer["Note must be signed first"], Severity.Error);
                            return;
                        }

                        _existingCosigning.IsCosigned = true;
                        _existingCosigning.CosignerName = _selectedToProvider.UserName;
                        _existingCosigning.CosignerId = _selectedToProvider.Id;
                        _existingCosigning.Date = now;
                    }
                    else
                    {
                        _existingCosigning.IsSigned = true;
                        _existingCosigning.LastUpdated = now;
                        _existingCosigning.SignerName = pcpName;
                        _existingCosigning.SignerId = Guid.Parse(_ActiveUser.id);
                    }

                    await CosigningService.UpdateCosigning(_existingCosigning);
                }

                Snackbar.Add(Localizer["Changes saved successfully"], Severity.Success);
                flag = true;
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["Save failed"], Severity.Error);
                _logger.LogError(ex, "Error saving cosigning data");
            }
        }

        private async Task HandleOk()
        {
            try
            {
                if (Dense_Radio && _selectedToProvider == null)
                {
                    Snackbar.Add(_localizer["Select provider for cosigning first"], Severity.Warning);
                    return;
                }

                var now = DateTime.Now;

                if (Dense_Radio) // cosign
                {
                    cosignedText = $"Electronically co-signed {_selectedToProvider.UserName} on {now:MM/dd/yyyy}\n";
                }
                else // sign
                {
                    signedText = $"Electronically signed by {pcpName} on {now:MM/dd/yyyy}\n";
                }

                await SaveData();

                if (flag)
                {
                    patientNotes = string.Empty;
                    patientNotes = notesBody + signedText + cosignedText;
                    richTextContent = string.Empty;
                    richTextContent = signedText + cosignedText;
                    Snackbar.Add(Localizer["Signature added successfully"], Severity.Success);
                }

                flag = false;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding signature");
                Snackbar.Add(Localizer["Failed to add signature"], Severity.Error);
            }
        }


        private async Task HandleFinalize()
        {

            if (_isLocked)
            {
                Snackbar.Add(Localizer["Document is locked!"], Severity.Success);
                return;
            }

            if ((_existingCosigning == null) || (!_existingCosigning.IsSigned))
            {
                Snackbar.Add(Localizer["Complete signing/cosigning first"], Severity.Warning);
                return;
            }

            try
            {
                _isLocked = true;
                await UpdateLockStatus(true);
                richTextContent += $"Notes Locked!! on {DateTime.Now:MM/dd/yyyy}";


                Snackbar.Add(Localizer["Document locked successfully"], Severity.Success);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["Failed to lock document"], Severity.Error);
                _logger.LogError(ex, "Document locking failed");
            }
            CancelData();
        }

        private async Task UpdateLockStatus(bool isLocked)
        {
            if (_existingCosigning != null)
            {
                _existingCosigning.IsLocked = isLocked;
                patientNotes = patientNotes + $"Notes Loocked!! on {DateTime.Now:MM/dd/yyyy}\n";
                _existingCosigning.Notes = patientNotes;
                await CosigningService.UpdateCosigning(_existingCosigning);
            }
        }

        private bool ValidateSigning()
        {
            if (Dense_Radio && _selectedToProvider == null)
            {
                Snackbar.Add(Localizer["SelectCosigner"], Severity.Error);
                return false;
            }

            if (_existingCosigning?.IsLocked ?? false)
            {
                Snackbar.Add(Localizer["DocumentLocked"], Severity.Error);
                return false;
            }

            return true;
        }


    }
}