using AlertsBusinessLayer;
using AlertsBusinessLayer.QueryHandler;
using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIBussinessLayerTestCases
{
    [TestFixture]
    public class CustomProcedureAlertsQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<CustomProcedureAlertsQueryHandler>> _mockLogger;
        private Mock<IStringLocalizer<CustomProcedureAlertsQueryHandler>> _mockLocalizer;
        private Mock<ICustomProcedureAlertsRepository> _mockCustomProcedureAlertsRepository;
        private CustomProcedureAlertsQueryHandler _handler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<CustomProcedureAlertsQueryHandler>>();
            _mockLocalizer = new Mock<IStringLocalizer<CustomProcedureAlertsQueryHandler>>();
            _mockCustomProcedureAlertsRepository = new Mock<ICustomProcedureAlertsRepository>();

            _mockUnitOfWork.Setup(u => u.CustomProcedureAlertsRepository).Returns(_mockCustomProcedureAlertsRepository.Object);

            _handler = new CustomProcedureAlertsQueryHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllCustomProcedureAlerts_ShouldReturnAllAlerts()
        {
            // Arrange
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Procedure Alert 1", Description = "Description 1" },
                new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Procedure Alert 2", Description = "Description 2" }
            };

            _mockCustomProcedureAlertsRepository.Setup(r => r.GetAllCustomProcedureAlertsAsync()).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetAllCustomProcedureAlerts();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(alerts));
        }

        [Test]
        public async Task GetAllCustomProcedureAlerts_WhenExceptionOccurs_ShouldPropagateException()
        {
            // Arrange
            var exceptionMessage = "Database error";
            _mockCustomProcedureAlertsRepository.Setup(r => r.GetAllCustomProcedureAlertsAsync())
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetAllCustomProcedureAlerts());
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }

        [Test]
        public async Task GetCustomProcedureAlertsById_WithExistingId_ShouldReturnAlert()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomProcedureAlerts { Id = alertId, Name = "Procedure Alert 1", Description = "Description 1" };

            _mockCustomProcedureAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync(alert);

            // Act
            var result = await _handler.GetCustomProcedureAlertsById(alertId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(alertId));
            Assert.That(result.Name, Is.EqualTo("Procedure Alert 1"));
            Assert.That(result.Description, Is.EqualTo("Description 1"));
        }

        [Test]
        public void GetCustomProcedureAlertsById_WithNonExistentId_ShouldThrowKeyNotFoundException()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomProcedureAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync((CustomProcedureAlerts)null);

            // Act & Assert
            Assert.ThrowsAsync<KeyNotFoundException>(() => _handler.GetCustomProcedureAlertsById(alertId));
        }


        

        [Test]
        public async Task GetActiveCustomProcedureAlertsByPatientId_ShouldReturnActiveAlertsForPatient()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "Procedure Alert 1", Description = "Description 1", IsActive = true },
                new CustomProcedureAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "Procedure Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockCustomProcedureAlertsRepository.Setup(r => r.GetActiveCustomProcedureAlertsByOrganizationIdAsync(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetActiveCustomProcedureAlertsByOrganizationId(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.All(a => a.OrganizationId == organizationId && a.IsActive), Is.True);
        }

       



        [Test]
        public void GetActiveCustomProcedureAlertsByPatientId_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database connection error";

            _mockCustomProcedureAlertsRepository.Setup(r => r.GetActiveCustomProcedureAlertsByOrganizationIdAsync(organizationId))
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetActiveCustomProcedureAlertsByOrganizationId(organizationId));
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }
    }
}
