using AlertsApi.Controllers;
using AlertsBusinessLayer;
using AlertsContracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIControllerTests
{
    [TestFixture]
    public class DXAlertsControllerTests
    {
        private Mock<IDXAlertsCommandHandler<DXAlerts>> _mockDXAlertsCommandHandler;
        private Mock<IDXAlertsQueryHandler<DXAlerts>> _mockDXAlertsQueryHandler;
        private Mock<ILogger<DXAlertsController>> _mockLogger;
        private Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>> _mockLocalizer;
        private DXAlertsController _controller;

        [SetUp]
        public void Setup()
        {
            _mockDXAlertsCommandHandler = new Mock<IDXAlertsCommandHandler<DXAlerts>>();
            _mockDXAlertsQueryHandler = new Mock<IDXAlertsQueryHandler<DXAlerts>>();
            _mockLogger = new Mock<ILogger<DXAlertsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>>();
            
            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));
            _mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()]).Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));
            
            _controller = new DXAlertsController(
                _mockDXAlertsCommandHandler.Object,
                _mockDXAlertsQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }


        [Test]
        public async Task GetAllById_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var Id = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockDXAlertsQueryHandler.Setup(h => h.GetDXAlertsById(Id)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllById(Id);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsOkResult_WithActiveAlerts()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<DXAlerts>
            {
                new DXAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "DX Alert 1", Description = "Description 1", IsActive = true },
                new DXAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "DX Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockDXAlertsQueryHandler.Setup(h => h.GetActiveDXAlertsByOrganizationId(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.Value, Is.EqualTo(alerts));
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockDXAlertsQueryHandler.Setup(h => h.GetActiveDXAlertsByOrganizationId(organizationId)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllByIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddDXAlert_ReturnsOkResult_WhenAlertsAdded()
        {
            // Arrange
            var alerts = new List<DXAlerts>
            {
                new DXAlerts { Id = Guid.NewGuid(), Name = "DX Alert 1", Description = "Description 1" },
                new DXAlerts { Id = Guid.NewGuid(), Name = "DX Alert 2", Description = "Description 2" }
            };

            _mockDXAlertsCommandHandler.Setup(h => h.AddDXAlerts(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddDXAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task AddDXAlert_ReturnsBadRequest_WhenNoAlertsProvided()
        {
            // Act
            var result = await _controller.AddDXAlert(new List<DXAlerts>());

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddDXAlert_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alerts = new List<DXAlerts>
            {
                new DXAlerts { Id = Guid.NewGuid(), Name = "DX Alert 1", Description = "Description 1" }
            };

            var exceptionMessage = "Database error";
            _mockDXAlertsCommandHandler.Setup(h => h.AddDXAlerts(alerts)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.AddDXAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateDXAlert_ReturnsOkResult_WhenAlertUpdated()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new DXAlerts { Id = alertId, Name = "Updated DX Alert", Description = "Updated Description" };

            _mockDXAlertsCommandHandler.Setup(h => h.UpdateDXAlerts(alert)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateDXAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task UpdateDXAlert_ReturnsBadRequest_WhenAlertIdMismatch()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new DXAlerts { Id = Guid.NewGuid(), Name = "Updated DX Alert", Description = "Updated Description" };

            // Act
            var result = await _controller.UpdateDXAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateDXAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new DXAlerts { Id = alertId, Name = "Updated DX Alert", Description = "Updated Description" };

            _mockDXAlertsCommandHandler.Setup(h => h.UpdateDXAlerts(alert)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateDXAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task UpdateDXAlertList_ReturnsOkResult_WhenAlertsUpdated()
        {
            // Arrange
            var alerts = new List<DXAlerts>
            {
                new DXAlerts { Id = Guid.NewGuid(), Name = "Updated DX Alert 1", Description = "Updated Description 1" },
                new DXAlerts { Id = Guid.NewGuid(), Name = "Updated DX Alert 2", Description = "Updated Description 2" }
            };

            _mockDXAlertsCommandHandler.Setup(h => h.UpdateDXAlertsList(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateDXAlertList(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task UpdateDXAlertList_ReturnsBadRequest_WhenNoAlertsProvided()
        {
            // Act
            var result = await _controller.UpdateDXAlertList(new List<DXAlerts>());

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task DeleteDXAlert_ReturnsOkResult_WhenAlertDeleted()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockDXAlertsCommandHandler.Setup(h => h.DeleteDXAlertsById(alertId)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteDXAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteDXAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockDXAlertsCommandHandler.Setup(h => h.DeleteDXAlertsById(alertId)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteDXAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteDXAlert_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var exceptionMessage = "Database error";

            _mockDXAlertsCommandHandler.Setup(h => h.DeleteDXAlertsById(alertId)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.DeleteDXAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task DeleteDXAlertByEntity_ReturnsOkResult_WhenAlertDeleted()
        {
            // Arrange
            var alert = new DXAlerts { Id = Guid.NewGuid(), Name = "DX Alert to delete", Description = "Description" };

            _mockDXAlertsCommandHandler.Setup(h => h.DeleteDXAlertsByEntity(alert)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteDXAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteDXAlertByEntity_ReturnsBadRequest_WhenAlertIsNull()
        {
            // Act
            var result = await _controller.DeleteDXAlertByEntity(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task DeleteDXAlertByEntity_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alert = new DXAlerts { Id = Guid.NewGuid(), Name = "DX Alert to delete", Description = "Description" };

            _mockDXAlertsCommandHandler.Setup(h => h.DeleteDXAlertsByEntity(alert)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteDXAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }
    }
}
