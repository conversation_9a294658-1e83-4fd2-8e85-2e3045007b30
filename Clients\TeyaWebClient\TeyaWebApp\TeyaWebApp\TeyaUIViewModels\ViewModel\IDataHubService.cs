using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IDataHubService
    {
        // Patient Registry Operations
        Task<List<PatientRegistryData>> GetPatientRegistryDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> SearchPatientsAsync(string searchTerm, Guid organizationId, bool subscription);
        Task<int> GetPatientCountAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        
        // Export Operations
        Task<string> ExportPatientRegistryToTextAsync(List<PatientRegistryData> patients);
        Task<byte[]> ExportPatientRegistryToCsvAsync(List<PatientRegistryData> patients);
        
        // Query Operations
        Task<List<PatientRegistryData>> ExecuteQueryAsync(DataHubQueryOperation queryOperation, Guid organizationId, bool subscription);
        Task<SavedQuery> SaveQueryAsync(SavedQuery query);
        Task<List<SavedQuery>> GetSavedQueriesAsync(Guid organizationId, Guid? userId = null);
        Task<SavedQuery> GetSavedQueryByIdAsync(Guid queryId);
        Task DeleteSavedQueryAsync(Guid queryId);
        
        // Registry Tab Operations
        Task<List<PatientRegistryData>> GetDemographicsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetVitalsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetLabsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetDiagnosticImagingDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetProceduresDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetICDDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetCPTDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetRxDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetImmunizationDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetEncountersDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetAllergiesDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetMedicalHistoryDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetStructuredDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetReferralsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
        Task<List<PatientRegistryData>> GetReportsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription);
    }
}
