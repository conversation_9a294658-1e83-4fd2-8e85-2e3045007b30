@using TeyaUIModels.Model
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<DataHubFilters> Localizer

<MudGrid>
    <!-- Search Term -->
    <MudItem xs="12" md="6">
        <MudTextField @bind-Value="Filter.SearchTerm"
                      Label="@Localizer["SearchTerm"]"
                      Placeholder="@Localizer["SearchPlaceholder"]"
                      Adornment="Adornment.Start"
                      AdornmentIcon="@Icons.Material.Filled.Search"
                      Immediate="true"
                      OnKeyPress="OnSearchKeyPress" />
    </MudItem>

    <!-- Quick Search Button -->
    <MudItem xs="12" md="6" Class="d-flex align-center">
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.Search"
                   OnClick="OnSearchClicked"
                   Class="ml-2">
            @Localizer["Search"]
        </MudButton>
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Secondary"
                   StartIcon="@Icons.Material.Filled.Clear"
                   OnClick="ClearFilters"
                   Class="ml-2">
            @Localizer["Clear"]
        </MudButton>
    </MudItem>

    <!-- Name Filters -->
    <MudItem xs="12" md="6">
        <MudTextField @bind-Value="Filter.FirstName"
                      Label="@Localizer["FirstName"]"
                      Immediate="true" />
    </MudItem>
    <MudItem xs="12" md="6">
        <MudTextField @bind-Value="Filter.LastName"
                      Label="@Localizer["LastName"]"
                      Immediate="true" />
    </MudItem>

    <!-- Contact Information -->
    <MudItem xs="12" md="6">
        <MudTextField @bind-Value="Filter.Email"
                      Label="@Localizer["Email"]"
                      Immediate="true" />
    </MudItem>
    <MudItem xs="12" md="6">
        <MudTextField @bind-Value="Filter.PhoneNumber"
                      Label="@Localizer["PhoneNumber"]"
                      Immediate="true" />
    </MudItem>

    <!-- Date of Birth Range -->
    <MudItem xs="12" md="6">
        <MudDatePicker @bind-Date="Filter.DateOfBirthFrom"
                       Label="@Localizer["DateOfBirthFrom"]"
                       DateFormat="yyyy-MM-dd" />
    </MudItem>
    <MudItem xs="12" md="6">
        <MudDatePicker @bind-Date="Filter.DateOfBirthTo"
                       Label="@Localizer["DateOfBirthTo"]"
                       DateFormat="yyyy-MM-dd" />
    </MudItem>

    <!-- Gender -->
    <MudItem xs="12" md="6">
        <MudSelect @bind-Value="Filter.Gender"
                   Label="@Localizer["Gender"]"
                   Clearable="true">
            <MudSelectItem Value="@("Male")">@Localizer["Male"]</MudSelectItem>
            <MudSelectItem Value="@("Female")">@Localizer["Female"]</MudSelectItem>
            <MudSelectItem Value="@("Other")">@Localizer["Other"]</MudSelectItem>
        </MudSelect>
    </MudItem>

    <!-- Active Status -->
    <MudItem xs="12" md="6">
        <MudSelect @bind-Value="Filter.IsActive"
                   Label="@Localizer["Status"]"
                   Clearable="true">
            <MudSelectItem Value="@(true)">@Localizer["Active"]</MudSelectItem>
            <MudSelectItem Value="@(false)">@Localizer["Inactive"]</MudSelectItem>
        </MudSelect>
    </MudItem>

    <!-- Address Filters -->
    <MudItem xs="12" md="4">
        <MudTextField @bind-Value="Filter.City"
                      Label="@Localizer["City"]"
                      Immediate="true" />
    </MudItem>
    <MudItem xs="12" md="4">
        <MudTextField @bind-Value="Filter.State"
                      Label="@Localizer["State"]"
                      Immediate="true" />
    </MudItem>
    <MudItem xs="12" md="4">
        <MudTextField @bind-Value="Filter.ZipCode"
                      Label="@Localizer["ZipCode"]"
                      Immediate="true" />
    </MudItem>

    <!-- Provider Information -->
    <MudItem xs="12" md="6">
        <MudTextField @bind-Value="Filter.PrimaryProvider"
                      Label="@Localizer["PrimaryProvider"]"
                      Immediate="true" />
    </MudItem>
    <MudItem xs="12" md="6">
        <MudTextField @bind-Value="Filter.InsuranceProvider"
                      Label="@Localizer["InsuranceProvider"]"
                      Immediate="true" />
    </MudItem>

    <!-- Last Visit Range -->
    <MudItem xs="12" md="6">
        <MudDatePicker @bind-Date="Filter.LastVisitFrom"
                       Label="@Localizer["LastVisitFrom"]"
                       DateFormat="yyyy-MM-dd" />
    </MudItem>
    <MudItem xs="12" md="6">
        <MudDatePicker @bind-Date="Filter.LastVisitTo"
                       Label="@Localizer["LastVisitTo"]"
                       DateFormat="yyyy-MM-dd" />
    </MudItem>

    <!-- Pagination and Sorting -->
    <MudItem xs="12" md="4">
        <MudNumericField @bind-Value="Filter.PageSize"
                         Label="@Localizer["PageSize"]"
                         Min="10"
                         Max="500"
                         Step="10" />
    </MudItem>
    <MudItem xs="12" md="4">
        <MudSelect @bind-Value="Filter.SortBy"
                   Label="@Localizer["SortBy"]">
            <MudSelectItem Value="@("FirstName")">@Localizer["FirstName"]</MudSelectItem>
            <MudSelectItem Value="@("LastName")">@Localizer["LastName"]</MudSelectItem>
            <MudSelectItem Value="@("Email")">@Localizer["Email"]</MudSelectItem>
            <MudSelectItem Value="@("DateOfBirth")">@Localizer["DateOfBirth"]</MudSelectItem>
            <MudSelectItem Value="@("CreatedAt")">@Localizer["CreatedAt"]</MudSelectItem>
        </MudSelect>
    </MudItem>
    <MudItem xs="12" md="4">
        <MudSwitch @bind-Checked="Filter.SortDescending"
                   Label="@Localizer["SortDescending"]"
                   Color="Color.Primary" />
    </MudItem>
</MudGrid>

@code {
    [Parameter] public PatientRegistryFilter Filter { get; set; } = new();
    [Parameter] public EventCallback<PatientRegistryFilter> OnFilterChanged { get; set; }
    [Parameter] public EventCallback OnSearch { get; set; }

    private async Task OnSearchClicked()
    {
        await NotifyFilterChanged();
        await OnSearch.InvokeAsync();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await OnSearchClicked();
        }
    }

    private async Task ClearFilters()
    {
        Filter = new PatientRegistryFilter
        {
            PageSize = 50,
            PageNumber = 1,
            SortBy = "LastName",
            SortDescending = false
        };
        
        await NotifyFilterChanged();
    }

    private async Task NotifyFilterChanged()
    {
        await OnFilterChanged.InvokeAsync(Filter);
    }

    protected override async Task OnParametersSetAsync()
    {
        await NotifyFilterChanged();
    }
}
