@using TeyaUIModels.Model
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<DataHubTabContent> Localizer

<MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
    <MudText Typo="Typo.h6" Class="mb-3">
        @GetTabDisplayName(TabName)
    </MudText>

    @switch (TabName)
    {
        case "Demographics":
            <DemographicsTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Vitals":
            <VitalsTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Labs":
            <LabsTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "DiagnosticImaging":
            <DiagnosticImagingTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Procedures":
            <ProceduresTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "ICD":
            <ICDTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "CPT":
            <CPTTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Rx":
            <RxTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Immunization":
            <ImmunizationTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Encounters":
            <EncountersTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Allergies":
            <AllergiesTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "MedicalHistory":
            <MedicalHistoryTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "StructuredData":
            <StructuredDataTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Referrals":
            <ReferralsTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        case "Reports":
            <ReportsTabContent Filter="@Filter" OnFilterChanged="@OnFilterChanged" />
            break;
        
        default:
            <MudAlert Severity="Severity.Info">
                @Localizer["TabContentNotImplemented", TabName]
            </MudAlert>
            break;
    }
</MudContainer>

@code {
    [Parameter] public string TabName { get; set; } = string.Empty;
    [Parameter] public PatientRegistryFilter Filter { get; set; } = new();
    [Parameter] public EventCallback<PatientRegistryFilter> OnFilterChanged { get; set; }

    private string GetTabDisplayName(string tabName)
    {
        return tabName switch
        {
            "Demographics" => Localizer["Demographics"],
            "Vitals" => Localizer["Vitals"],
            "Labs" => Localizer["LabsDIProcedure"],
            "DiagnosticImaging" => Localizer["DiagnosticImaging"],
            "Procedures" => Localizer["Procedures"],
            "ICD" => Localizer["ICD"],
            "CPT" => Localizer["CPT"],
            "Rx" => Localizer["Rx"],
            "Immunization" => Localizer["Immunization"],
            "Encounters" => Localizer["Encounters"],
            "Allergies" => Localizer["Allergies"],
            "MedicalHistory" => Localizer["MedicalHistory"],
            "StructuredData" => Localizer["StructuredData"],
            "Referrals" => Localizer["Referrals"],
            "Reports" => Localizer["Reports"],
            _ => tabName
        };
    }
}

<!-- Demographics Tab Content -->
@if (TabName == "Demographics")
{
    <MudGrid>
        <MudItem xs="12">
            <MudText Typo="Typo.body1" Class="mb-3">
                @Localizer["DemographicsDescription"]
            </MudText>
        </MudItem>
        
        <MudItem xs="12" md="6">
            <MudTextField @bind-Value="Filter.FirstName"
                          Label="@Localizer["FirstName"]"
                          Immediate="true"
                          OnTextChanged="@(() => OnFilterChanged.InvokeAsync(Filter))" />
        </MudItem>
        
        <MudItem xs="12" md="6">
            <MudTextField @bind-Value="Filter.LastName"
                          Label="@Localizer["LastName"]"
                          Immediate="true"
                          OnTextChanged="@(() => OnFilterChanged.InvokeAsync(Filter))" />
        </MudItem>
        
        <MudItem xs="12" md="6">
            <MudDatePicker @bind-Date="Filter.DateOfBirthFrom"
                           Label="@Localizer["DateOfBirthFrom"]"
                           DateFormat="yyyy-MM-dd" />
        </MudItem>
        
        <MudItem xs="12" md="6">
            <MudDatePicker @bind-Date="Filter.DateOfBirthTo"
                           Label="@Localizer["DateOfBirthTo"]"
                           DateFormat="yyyy-MM-dd" />
        </MudItem>
        
        <MudItem xs="12" md="6">
            <MudSelect @bind-Value="Filter.Gender"
                       Label="@Localizer["Gender"]"
                       Clearable="true">
                <MudSelectItem Value="@("Male")">@Localizer["Male"]</MudSelectItem>
                <MudSelectItem Value="@("Female")">@Localizer["Female"]</MudSelectItem>
                <MudSelectItem Value="@("Other")">@Localizer["Other"]</MudSelectItem>
            </MudSelect>
        </MudItem>
        
        <MudItem xs="12" md="6">
            <MudTextField @bind-Value="Filter.City"
                          Label="@Localizer["City"]"
                          Immediate="true"
                          OnTextChanged="@(() => OnFilterChanged.InvokeAsync(Filter))" />
        </MudItem>
    </MudGrid>
}

<!-- For other tabs, we'll show a placeholder for now -->
@if (TabName != "Demographics")
{
    <MudAlert Severity="Severity.Info" Class="mb-4">
        @Localizer["TabSpecificFiltersWillBeImplemented", GetTabDisplayName(TabName)]
    </MudAlert>
    
    <MudGrid>
        <MudItem xs="12">
            <MudTextField @bind-Value="Filter.SearchTerm"
                          Label="@Localizer["SearchInTab", GetTabDisplayName(TabName)]"
                          Placeholder="@Localizer["SearchPlaceholder"]"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search"
                          Immediate="true"
                          OnTextChanged="@(() => OnFilterChanged.InvokeAsync(Filter))" />
        </MudItem>
    </MudGrid>
}
