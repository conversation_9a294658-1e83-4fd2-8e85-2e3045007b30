﻿using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

var builder = WebApplication.CreateBuilder(args);

var environment = Environment.GetEnvironmentVariable("ENVIRONMENT_KEY") ?? "Local";

var configBuilder = new ConfigurationBuilder();

if (environment.Equals("Local", StringComparison.OrdinalIgnoreCase))
{
    configBuilder
        .AddJsonFile("appsettings.json", optional: true)
        .AddJsonFile("appsettings.Local.json", optional: true)
        .AddEnvironmentVariables();
}
else
{
    configBuilder.AddEnvironmentVariables();
}

var config = configBuilder.Build();

if (environment.Equals("Local", StringComparison.OrdinalIgnoreCase))
{
    var localSection = config.GetSection("EnvironmentVariables");
    foreach (var child in localSection.GetChildren())
    {
        Environment.SetEnvironmentVariable(child.Key, child.Value);
    }
}

if (environment.Equals("Development", StringComparison.OrdinalIgnoreCase) ||
    environment.Equals("Production", StringComparison.OrdinalIgnoreCase))
{
    await LoadSecretsAsync(config);
}

string loginUrl = Environment.GetEnvironmentVariable("LoginUrl") ?? throw new Exception("LOGIN_URL not set");

var app = builder.Build();

app.UseRouting();

app.Use(async (context, next) =>
{
    var path = context.Request.Path.Value;

    if (!string.IsNullOrEmpty(path) && !Path.HasExtension(path))
    {
        var originalPath = path.Trim('/');
        var lowerPath = originalPath.ToLowerInvariant();
        var physicalPath = Path.Combine(app.Environment.WebRootPath, $"{lowerPath}.html");

        if (System.IO.File.Exists(physicalPath))
        {
            context.Request.Path = $"/{lowerPath}.html";
        }
    }

    await next();
});

app.UseStaticFiles();

app.MapGet("/", context =>
{
    context.Response.Redirect("/Home");
    return Task.CompletedTask;
});

app.MapGet("/Redlogin", context =>
{
    if (string.IsNullOrEmpty(loginUrl))
    {
        context.Response.StatusCode = 500;
        return context.Response.WriteAsync("Login URL is not configured.");
    }

    context.Response.Redirect(loginUrl);
    return Task.CompletedTask;
});

app.Run();

static async Task LoadSecretsAsync(IConfiguration config)
{
    var keyvault_urls = Environment.GetEnvironmentVariable("KEYVAULT__URLS");
    var tenantId = Environment.GetEnvironmentVariable("KEYVAULT__TENANTID");
    var clientId = Environment.GetEnvironmentVariable("KEYVAULT__CLIENTID");
    var clientSecret = Environment.GetEnvironmentVariable("KEYVAULT__CLIENTSECRET");

    var list_keyvault_urls = keyvault_urls.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

    var credential = new ClientSecretCredential(
        tenantId,
        clientId,
        clientSecret,
        new ClientSecretCredentialOptions { AdditionallyAllowedTenants = { "*" } });

    foreach (var keyvaulturl in list_keyvault_urls)
    {
        var client = new SecretClient(new Uri(keyvaulturl), credential);

        await foreach (var secretProperties in client.GetPropertiesOfSecretsAsync())
        {
            var secret = await client.GetSecretAsync(secretProperties.Name);
            Environment.SetEnvironmentVariable(secretProperties.Name, secret.Value.Value);
        }
    }
}
