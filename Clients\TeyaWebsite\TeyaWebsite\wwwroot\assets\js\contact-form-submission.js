﻿document.addEventListener("DOMContentLoaded", function () {
    const form = document.getElementById("contactForm");

    if (!form) return;

    form.addEventListener("submit", function (event) {
        event.preventDefault();

        const data = {
            fullName: form.querySelector('[name="Full Name"]').value,
            organizationName: form.querySelector('[name="Organization Name"]').value,
            email: form.querySelector('[name="Email"]').value,
            message: form.querySelector('[name="Message"]').value
        };

        fetch("http://localhost/MemberServiceApi/api/contact/send", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data)
        })
            .then(async (response) => {
                if (response.ok) {
                    Swal.fire({
                        position: "center",
                        icon: "success",
                        title: "Message Sent!",
                        text: "Thanks for reaching out to us.",
                        showConfirmButton: false,
                        timer: 2500,
                        background: "#f0f9ff",
                        color: "#1e3a8a",
                        backdrop: `
                        rgba(0,0,0,0.4)
                        left top
                        no-repeat
  `,
                        timerProgressBar: true,
                        showClass: {
                            popup: 'swal2-show swal2-animate__fadeInDown'
                        },
                        hideClass: {
                            popup: 'swal2-hide swal2-animate__fadeOutUp'
                        }
                    });


                    form.reset();
                } else {
                    const error = await response.text();
                    alert("Failed to send message: " + error);
                }
            })
            .catch((error) => {
                console.error("Error:", error);
                alert("Something went wrong. Please try again later.");
            });
    });
});
