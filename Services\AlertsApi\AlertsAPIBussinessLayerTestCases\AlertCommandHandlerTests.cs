using AlertsBusinessLayer.CommandHandler;
using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AlertsAPIBussinessLayerTestCases
{
    [TestFixture]
    public class AlertCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<AlertCommandHandler>> _mockLogger;
        private Mock<IStringLocalizer<AlertCommandHandler>> _mockLocalizer;
        private Mock<IAlertRepository> _mockAlertRepository;
        private AlertCommandHandler _handler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<AlertCommandHandler>>();
            _mockLocalizer = new Mock<IStringLocalizer<AlertCommandHandler>>();
            _mockAlertRepository = new Mock<IAlertRepository>();

            _mockUnitOfWork.Setup(u => u.AlertRepository).Returns(_mockAlertRepository.Object);

            _handler = new AlertCommandHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task AddAlerts_ShouldCallRepositoryMethod()
        {
            // Arrange
            var alerts = new List<Alert>
            {
                new Alert { AlertId = Guid.NewGuid(), PatientName = "John Doe", Description = "Alert 1" },
                new Alert { AlertId = Guid.NewGuid(), PatientName = "Jane Doe", Description = "Alert 2" }
            };

            _mockAlertRepository.Setup(r => r.AddAsync(It.IsAny<Alert>())).Returns(Task.CompletedTask);
            _mockUnitOfWork.Setup(u => u.SaveAsync()).ReturnsAsync(2);

            // Act
            await _handler.AddAlerts(alerts);

            // Assert
            foreach (var alert in alerts)
            {
                _mockAlertRepository.Verify(r => r.AddAsync(alert), Times.Once);
            }
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateAlert_ShouldCallRepositoryMethodAndSaveChanges()
        {
            // Arrange
            var alert = new Alert { AlertId = Guid.NewGuid(), PatientName = "John Doe", Description = "Updated Alert" };

            _mockAlertRepository.Setup(r => r.GetByIdAsync(alert.AlertId)).ReturnsAsync(alert);
            _mockAlertRepository.Setup(r => r.UpdateAsync(It.IsAny<Alert>())).Returns(Task.CompletedTask);
            _mockUnitOfWork.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            await _handler.UpdateAlert(alert);

            // Assert
            _mockAlertRepository.Verify(r => r.GetByIdAsync(alert.AlertId), Times.Once);
            _mockAlertRepository.Verify(r => r.UpdateAsync(alert), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void UpdateAlert_WithNonExistentAlert_ShouldThrowKeyNotFoundException()
        {
            // Arrange
            var alert = new Alert { AlertId = Guid.NewGuid(), PatientName = "John Doe", Description = "Updated Alert" };

            _mockAlertRepository.Setup(r => r.GetByIdAsync(alert.AlertId)).ReturnsAsync((Alert)null);

            // Act & Assert
            Assert.ThrowsAsync<KeyNotFoundException>(() => _handler.UpdateAlert(alert));
        }

        [Test]
        public async Task DeleteAlertById_ShouldCallRepositoryMethodAndSaveChanges()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new Alert { AlertId = alertId, PatientName = "John Doe", Description = "Alert to delete" };

            _mockAlertRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync(alert);
            _mockAlertRepository.Setup(r => r.DeleteAsync(alertId)).Returns(Task.CompletedTask);
            _mockUnitOfWork.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            await _handler.DeleteAlertById(alertId);

            // Assert
            _mockAlertRepository.Verify(r => r.GetByIdAsync(alertId), Times.Once);
            _mockAlertRepository.Verify(r => r.DeleteAsync(alertId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void DeleteAlertById_WithNonExistentAlert_ShouldThrowKeyNotFoundException()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockAlertRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync((Alert)null);

            // Act & Assert
            Assert.ThrowsAsync<KeyNotFoundException>(() => _handler.DeleteAlertById(alertId));
        }

        [Test]
        public async Task DeleteAlertByEntity_ShouldCallRepositoryMethodAndSaveChanges()
        {
            // Arrange
            var alert = new Alert { AlertId = Guid.NewGuid(), PatientName = "John Doe", Description = "Alert to delete" };

            _mockAlertRepository.Setup(r => r.GetByIdAsync(alert.AlertId)).ReturnsAsync(alert);
            _mockAlertRepository.Setup(r => r.UpdateAsync(It.IsAny<Alert>())).Returns(Task.CompletedTask);
            _mockUnitOfWork.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            await _handler.DeleteAlertByEntity(alert);

            // Assert
            _mockAlertRepository.Verify(r => r.GetByIdAsync(alert.AlertId), Times.Once);
            _mockAlertRepository.Verify(r => r.UpdateAsync(It.Is<Alert>(a => a.AlertId == alert.AlertId && a.IsActive == false)), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateAlertsList_ShouldUpdateExistingAlertsAndSkipNonExistent()
        {
            // Arrange
            var existingAlertId = Guid.NewGuid();
            var nonExistentAlertId = Guid.NewGuid();

            var alerts = new List<Alert>
            {
                new Alert { AlertId = existingAlertId, PatientName = "John Doe", Description = "Updated Alert 1" },
                new Alert { AlertId = nonExistentAlertId, PatientName = "Jane Doe", Description = "Updated Alert 2" }
            };

            _mockAlertRepository.Setup(r => r.GetByIdAsync(existingAlertId)).ReturnsAsync(new Alert { AlertId = existingAlertId });
            _mockAlertRepository.Setup(r => r.GetByIdAsync(nonExistentAlertId)).ReturnsAsync((Alert)null);
            _mockAlertRepository.Setup(r => r.UpdateAsync(It.IsAny<Alert>())).Returns(Task.CompletedTask);
            _mockUnitOfWork.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            await _handler.UpdateAlertsList(alerts);

            // Assert
            _mockAlertRepository.Verify(r => r.GetByIdAsync(existingAlertId), Times.Once);
            _mockAlertRepository.Verify(r => r.GetByIdAsync(nonExistentAlertId), Times.Once);
            _mockAlertRepository.Verify(r => r.UpdateAsync(It.Is<Alert>(a => a.AlertId == existingAlertId)), Times.Once);
            _mockAlertRepository.Verify(r => r.UpdateAsync(It.Is<Alert>(a => a.AlertId == nonExistentAlertId)), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        




    }
}
