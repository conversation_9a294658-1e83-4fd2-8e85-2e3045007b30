// DataHub JavaScript Functions

window.downloadFile = (fileName, contentType, content) => {
    try {
        // Create a blob with the content
        const blob = new Blob([content], { type: contentType });
        
        // Create a temporary URL for the blob
        const url = window.URL.createObjectURL(blob);
        
        // Create a temporary anchor element and trigger download
        const anchor = document.createElement('a');
        anchor.href = url;
        anchor.download = fileName;
        anchor.style.display = 'none';
        
        // Append to body, click, and remove
        document.body.appendChild(anchor);
        anchor.click();
        document.body.removeChild(anchor);
        
        // Clean up the URL
        window.URL.revokeObjectURL(url);
        
        console.log(`File downloaded: ${fileName}`);
    } catch (error) {
        console.error('Error downloading file:', error);
        alert('Error downloading file. Please try again.');
    }
};

window.downloadFileFromBytes = (fileName, contentType, byteArray) => {
    try {
        // Convert byte array to Uint8Array
        const uint8Array = new Uint8Array(byteArray);
        
        // Create a blob with the byte array
        const blob = new Blob([uint8Array], { type: contentType });
        
        // Create a temporary URL for the blob
        const url = window.URL.createObjectURL(blob);
        
        // Create a temporary anchor element and trigger download
        const anchor = document.createElement('a');
        anchor.href = url;
        anchor.download = fileName;
        anchor.style.display = 'none';
        
        // Append to body, click, and remove
        document.body.appendChild(anchor);
        anchor.click();
        document.body.removeChild(anchor);
        
        // Clean up the URL
        window.URL.revokeObjectURL(url);
        
        console.log(`File downloaded: ${fileName}`);
    } catch (error) {
        console.error('Error downloading file from bytes:', error);
        alert('Error downloading file. Please try again.');
    }
};

window.copyToClipboard = (text) => {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // Use the modern clipboard API
            navigator.clipboard.writeText(text).then(() => {
                console.log('Text copied to clipboard');
            }).catch(err => {
                console.error('Failed to copy text: ', err);
                fallbackCopyToClipboard(text);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyToClipboard(text);
        }
    } catch (error) {
        console.error('Error copying to clipboard:', error);
        fallbackCopyToClipboard(text);
    }
};

function fallbackCopyToClipboard(text) {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
            console.log('Text copied to clipboard (fallback)');
        } else {
            console.error('Failed to copy text (fallback)');
        }
    } catch (error) {
        console.error('Fallback copy failed:', error);
    }
}

window.printElement = (elementId) => {
    try {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error(`Element with ID '${elementId}' not found`);
            return;
        }
        
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Print</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        @media print {
                            body { margin: 0; }
                        }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
        
    } catch (error) {
        console.error('Error printing element:', error);
        alert('Error printing. Please try again.');
    }
};

window.exportTableToCSV = (tableId, fileName) => {
    try {
        const table = document.getElementById(tableId);
        if (!table) {
            console.error(`Table with ID '${tableId}' not found`);
            return;
        }
        
        let csv = [];
        const rows = table.querySelectorAll('tr');
        
        for (let i = 0; i < rows.length; i++) {
            const row = [];
            const cols = rows[i].querySelectorAll('td, th');
            
            for (let j = 0; j < cols.length; j++) {
                let cellText = cols[j].innerText.replace(/"/g, '""');
                row.push(`"${cellText}"`);
            }
            
            csv.push(row.join(','));
        }
        
        const csvContent = csv.join('\n');
        downloadFile(fileName || 'table-export.csv', 'text/csv', csvContent);
        
    } catch (error) {
        console.error('Error exporting table to CSV:', error);
        alert('Error exporting table. Please try again.');
    }
};

window.scrollToElement = (elementId) => {
    try {
        const element = document.getElementById(elementId);
        if (element) {
            element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }
    } catch (error) {
        console.error('Error scrolling to element:', error);
    }
};

window.highlightElement = (elementId, duration = 2000) => {
    try {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.transition = 'background-color 0.3s ease';
            element.style.backgroundColor = '#ffeb3b';
            
            setTimeout(() => {
                element.style.backgroundColor = '';
                setTimeout(() => {
                    element.style.transition = '';
                }, 300);
            }, duration);
        }
    } catch (error) {
        console.error('Error highlighting element:', error);
    }
};

// Initialize DataHub functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DataHub JavaScript functions loaded');
});

// Export functions for module usage if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        downloadFile,
        downloadFileFromBytes,
        copyToClipboard,
        printElement,
        exportTableToCSV,
        scrollToElement,
        highlightElement
    };
}
