using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Layout
{
    public partial class AdminNav : ComponentBase
    {
        private readonly string Menu = "/Menu";
        private readonly string Chart = "/Chart";
        private readonly string Appointments = "/Appointments";
        private readonly string OrderSets = "/OrderSets";
        private readonly string Message = "/Message";
        private readonly string Practice = "/Practice";
        private readonly string Document = "/Document";
        private readonly string Providers = "/Providers";
        private readonly string Patients = "/Patients";
        private readonly string PlanBilling = "/PlanBilling";
        private readonly string Staff = "/Staff";
        private readonly string LicenseActivation = "/LicenseActivation";
        private readonly string License = "/License";
        private readonly string ProductFeatureSettings = "/ProductFeatureSettings";
        private readonly string Security = "/Security";
        private readonly string UserManagement = "/UserManagement";
        private readonly string Templates = "/Templates";
        private readonly string Config = "/Config";
        private readonly string About = "/about";
        private readonly string Registry = "/Registry";

        private readonly string ClaimsLookup = "/ClaimsLookup";
        private readonly string BillingEncounters = "/BillingEncounters";

        private const string EHR_PRODUCT = "EHR";
        private const string BILLING_PRODUCT = "Billing";
        private const string adminRole = "Admin";

        private List<string> AvailableProducts = new();
        private bool HasMultipleProducts = false;

        private static readonly Dictionary<string, HashSet<string>> ProductPageMappings = new()
        {
            [EHR_PRODUCT] = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "/Menu", "/Chart", "/Appointments", "/OrderSets", "/Message", "/Practice",
                "/Document", "/Providers", "/Patients", "/PlanBilling", "/Staff",
                "/LicenseActivation", "/License", "/ProductFeatureSettings", "/Security",
                "/UserManagement", "/Templates", "/Config", "/About", "/Registry", "/DataHub"
            },
            [BILLING_PRODUCT] = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "/ClaimsLookup", "/BillingEncounters"
            }
        };

        private bool ShouldRenderLicenseLink = false;
        private List<ProductOrganizationMapping> UserProductMappings = new();
        private List<Product> AllProducts = new();
        private TeyaUIModels.Model.Organization CurrentOrganization = null;
        private bool HasEHRAccess = false;
        private bool HasBillingAccess = false;

        [Inject] private IMemberService MemberService { get; set; } = default!;
        [Inject] private IPageRoleMappingService PageRoleMappingService { get; set; } = default!;
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IPreDefinedPageRoleMappingService PreDefinedPageRoleMappingService { get; set; } = default!;
        [Inject] private ILogger<AdminNav> Logger { get; set; } = default!;
        [Inject] private RoleMappingState RoleMappingState { get; set; }
        [Inject] private ActiveUser activeUser { get; set; }
        private bool Subscription = false;
        [Inject] private IProductOrganizationMappingService ProductOrganizationMappingService { get; set; }
        [Inject] private IProductService ProductService { get; set; }
        private List<Member> members = new();
        private List<PageRoleMappingData> PageRoleMappings = new();
        private List<PreDefinedPageRoleMappingData> PreDefinedPageRoleMappings = new();

        [Parameter] public string SelectedProduct { get; set; } = "EHR";

        public bool HasEHRProductAccess() => HasEHRAccess;
        public bool HasBillingProductAccess() => HasBillingAccess;
        public bool ShouldShowProductSwitcher() => false;
        public string GetSelectedProduct() => SelectedProduct;
        public bool IsEHRSelected() => SelectedProduct == "EHR";
        public bool IsBillingSelected() => SelectedProduct == "Billing";
        private bool _isLoading = true;
        private bool _showNoProductMessage = false;

        protected override async Task OnInitializedAsync()
        {
            await LoadProductsAndMappingsAsync();
            await LoadPageRoleMappingsAsync();

            var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
            var queryParams = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);

            if (queryParams.TryGetValue("product", out var productFromQuery))
            {
                SelectedProduct = productFromQuery.ToString();
            }

            await InitializeProductSwitcher();


            RoleMappingState.OnChange += async () =>
            {
                await LoadPageRoleMappingsAsync();
                await InvokeAsync(StateHasChanged);
            };

            await CheckUserAccess();

            _isLoading = false;
            _showNoProductMessage = !HasEHRAccess && !HasBillingAccess;

            StateHasChanged();
        }

        private async Task LoadProductsAndMappingsAsync()
        {
            try
            {
                AllProducts = await ProductService.GetProductsAsync() ?? new List<Product>();

                if (!string.IsNullOrEmpty(activeUser.OrganizationName))
                {
                    var organizationId = await OrganizationService.GetOrganizationIdByNameAsync(activeUser.OrganizationName);
                    if (organizationId != Guid.Empty)
                    {
                        CurrentOrganization = await OrganizationService.GetOrganizationByIdAsync(organizationId);

                        var allMappings = await ProductOrganizationMappingService.GetAllProductOrganizationMappingsAsync();
                        UserProductMappings = allMappings?.Where(m => m.OrganizationId == organizationId && m.IsActive).ToList()
                                           ?? new List<ProductOrganizationMapping>();

                        await DetermineProductAccess();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading products and mappings: {ex.Message}");
            }
        }

        private async Task DetermineProductAccess()
        {
            try
            {
                var ehrProduct = AllProducts.FirstOrDefault(p => p.Name.Equals(EHR_PRODUCT, StringComparison.OrdinalIgnoreCase));
                var billingProduct = AllProducts.FirstOrDefault(p => p.Name.Equals(BILLING_PRODUCT, StringComparison.OrdinalIgnoreCase));

                if (ehrProduct != null)
                {
                    HasEHRAccess = UserProductMappings.Any(m => m.ProductId == ehrProduct.Id);
                }

                if (billingProduct != null)
                {
                    HasBillingAccess = UserProductMappings.Any(m => m.ProductId == billingProduct.Id);
                }

                Logger.LogInformation($"Product Access - EHR: {HasEHRAccess}, Billing: {HasBillingAccess}");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error determining product access: {ex.Message}");
            }
        }

        private async Task InitializeProductSwitcher()
        {
            AvailableProducts.Clear();

            if (HasEHRAccess)
            {
                AvailableProducts.Add(EHR_PRODUCT);
            }

            if (HasBillingAccess)
            {
                AvailableProducts.Add(BILLING_PRODUCT);
            }

            HasMultipleProducts = AvailableProducts.Count > 1;

            if (AvailableProducts.Any() && string.IsNullOrEmpty(SelectedProduct))
            {
                SelectedProduct = AvailableProducts.First();
            }

            Logger.LogInformation($"Available Products: {string.Join(", ", AvailableProducts)}");
            Logger.LogInformation($"Selected Product: {SelectedProduct}");
        }

        private async Task CheckUserAccess()
        {
            if (activeUser?.mail?.EndsWith("@gmail.com") == true)
            {
                ShouldRenderLicenseLink = true;
            }
        }

        private async Task LoadPageRoleMappingsAsync()
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true)
            {
                var activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(activeUser.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];

                try
                {

                    members = await MemberService.GetAllMembersAsync(activeUserOrganizationId, Subscription);
                    var preferredName = user.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value;
                    var matchingMember = members.FirstOrDefault(m => m.Email == preferredName);

                    if (matchingMember != null)
                    {
                        activeUser.role = matchingMember.RoleName;

                        if (matchingMember.RoleName != adminRole)
                        {
                            var preDefinedMappings = await PreDefinedPageRoleMappingService.GetPagesByRoleNameAsync(matchingMember.RoleName);
                            PreDefinedPageRoleMappings = preDefinedMappings?.ToList() ?? new List<PreDefinedPageRoleMappingData>();
                        }
                        else if (matchingMember != null && matchingMember.RoleID.HasValue)
                        {
                            var pageRoleMappings = await PageRoleMappingService.GetPagesByRoleIdAsync(matchingMember.RoleID.Value, activeUserOrganizationId, Subscription);
                            PageRoleMappings = pageRoleMappings?.ToList() ?? new List<PageRoleMappingData>();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError($"Error during data loading: {ex.Message}");
                }
            }
        }

        public void Dispose()
        {
            RoleMappingState.OnChange -= async () => await InvokeAsync(StateHasChanged);
        }

        private bool IsPageAccessible(string pageUrl)
        {
            if (!IsPageForSelectedProduct(pageUrl))
            {
                return false;
            }

            if (activeUser.role == "Admin")
            {
                return PageRoleMappings != null && PageRoleMappings.Any(p => string.Equals(p.PagePath, pageUrl, StringComparison.OrdinalIgnoreCase));
            }
            else
            {
                return PreDefinedPageRoleMappings != null && PreDefinedPageRoleMappings.Any(p => string.Equals(p.PagePath, pageUrl, StringComparison.OrdinalIgnoreCase));
            }
        }

        private bool IsPageForSelectedProduct(string pageUrl)
        {
            if (string.IsNullOrEmpty(SelectedProduct))
                return false;

            return ProductPageMappings.TryGetValue(SelectedProduct, out var pages) && pages.Contains(pageUrl);
        }

        private string GetProductIcon(string product)
        {
            return product switch
            {
                "EHR" => Icons.Material.Filled.LocalHospital,
                "Billing" => Icons.Material.Filled.Receipt,
                _ => Icons.Material.Filled.Apps
            };
        }

        private string GetProductDisplayName(string product)
        {
            return product switch
            {
                "EHR" => Localizer["EHRProduct"],
                "Billing" => Localizer["BillingProduct"],
                _ => product
            };
        }

        private bool ShouldShowEHRSettings()
        {
            return IsPageAccessible(Providers) || IsPageAccessible(Patients) || IsPageAccessible(Staff) ||
                   IsPageAccessible(LicenseActivation) || IsPageAccessible(Security) || IsPageAccessible(UserManagement) ||
                   IsPageAccessible(Templates) || IsPageAccessible(Config) || IsPageAccessible(PlanBilling);
        }
    }
}