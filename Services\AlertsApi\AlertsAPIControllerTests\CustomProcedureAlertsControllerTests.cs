using AlertsApi.Controllers;
using AlertsBusinessLayer;
using AlertsContracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIControllerTests
{
    [TestFixture]
    public class CustomProcedureAlertsControllerTests
    {
        private Mock<ICustomProcedureAlertsCommandHandler<CustomProcedureAlerts>> _mockCustomProcedureAlertsCommandHandler;
        private Mock<ICustomProcedureAlertsQueryHandler<CustomProcedureAlerts>> _mockCustomProcedureAlertsQueryHandler;
        private Mock<ILogger<CustomProcedureAlertsController>> _mockLogger;
        private Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>> _mockLocalizer;
        private CustomProcedureAlertsController _controller;

        [SetUp]
        public void Setup()
        {
            _mockCustomProcedureAlertsCommandHandler = new Mock<ICustomProcedureAlertsCommandHandler<CustomProcedureAlerts>>();
            _mockCustomProcedureAlertsQueryHandler = new Mock<ICustomProcedureAlertsQueryHandler<CustomProcedureAlerts>>();
            _mockLogger = new Mock<ILogger<CustomProcedureAlertsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>>();
            
            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));
            _mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()]).Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));
            
            _controller = new CustomProcedureAlertsController(
                _mockCustomProcedureAlertsCommandHandler.Object,
                _mockCustomProcedureAlertsQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        


        [Test]
        public async Task GetAllById_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var Id = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockCustomProcedureAlertsQueryHandler.Setup(h => h.GetCustomProcedureAlertsById(Id)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllById(Id);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsOkResult_WithActiveAlerts()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "Procedure Alert 1", Description = "Description 1", IsActive = true },
                new CustomProcedureAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "Procedure Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockCustomProcedureAlertsQueryHandler.Setup(h => h.GetActiveCustomProcedureAlertsByOrganizationId(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.Value, Is.EqualTo(alerts));
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockCustomProcedureAlertsQueryHandler.Setup(h => h.GetActiveCustomProcedureAlertsByOrganizationId(organizationId)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllByIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddCustomProcedureAlert_ReturnsOkResult_WhenAlertsAdded()
        {
            // Arrange
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Procedure Alert 1", Description = "Description 1" },
                new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Procedure Alert 2", Description = "Description 2" }
            };

            _mockCustomProcedureAlertsCommandHandler.Setup(h => h.AddCustomProcedureAlerts(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddCustomProcedureAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task AddCustomProcedureAlert_ReturnsBadRequest_WhenNoAlertsProvided()
        {
            // Act
            var result = await _controller.AddCustomProcedureAlert(new List<CustomProcedureAlerts>());

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddCustomProcedureAlert_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Procedure Alert 1", Description = "Description 1" }
            };

            var exceptionMessage = "Database error";
            _mockCustomProcedureAlertsCommandHandler.Setup(h => h.AddCustomProcedureAlerts(alerts)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.AddCustomProcedureAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomProcedureAlert_ReturnsOkResult_WhenAlertUpdated()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomProcedureAlerts { Id = alertId, Name = "Updated Procedure Alert", Description = "Updated Description" };

            _mockCustomProcedureAlertsCommandHandler.Setup(h => h.UpdateCustomProcedureAlerts(alert)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateCustomProcedureAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task UpdateCustomProcedureAlert_ReturnsBadRequest_WhenAlertIdMismatch()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Updated Procedure Alert", Description = "Updated Description" };

            // Act
            var result = await _controller.UpdateCustomProcedureAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomProcedureAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomProcedureAlerts { Id = alertId, Name = "Updated Procedure Alert", Description = "Updated Description" };

            _mockCustomProcedureAlertsCommandHandler.Setup(h => h.UpdateCustomProcedureAlerts(alert)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateCustomProcedureAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task UpdateCustomProcedureAlertList_ReturnsOkResult_WhenAlertsUpdated()
        {
            // Arrange
            var alerts = new List<CustomProcedureAlerts>
            {
                new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Updated Procedure Alert 1", Description = "Updated Description 1" },
                new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Updated Procedure Alert 2", Description = "Updated Description 2" }
            };

            _mockCustomProcedureAlertsCommandHandler.Setup(h => h.UpdateCustomProcedureAlertsList(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateCustomProcedureAlertList(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomProcedureAlert_ReturnsOkResult_WhenAlertDeleted()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomProcedureAlertsCommandHandler.Setup(h => h.DeleteCustomProcedureAlertsById(alertId)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteCustomProcedureAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomProcedureAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomProcedureAlertsCommandHandler.Setup(h => h.DeleteCustomProcedureAlertsById(alertId)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteCustomProcedureAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteCustomProcedureAlertByEntity_ReturnsBadRequest_WhenAlertIsNull()
        {
            // Act
            var result = await _controller.DeleteCustomProcedureAlertByEntity(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task DeleteCustomProcedureAlertByEntity_ReturnsOkResult_WhenAlertDeleted()
        {
            // Arrange
            var alert = new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Test Procedure Alert" };

            _mockCustomProcedureAlertsCommandHandler
                .Setup(h => h.DeleteCustomProcedureAlertsByEntity(alert))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteCustomProcedureAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomProcedureAlertByEntity_ReturnsNotFound_WhenAlertNotFound()
        {
            // Arrange
            var alert = new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Missing Alert" };

            _mockCustomProcedureAlertsCommandHandler
                .Setup(h => h.DeleteCustomProcedureAlertsByEntity(alert))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteCustomProcedureAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteCustomProcedureAlertByEntity_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alert = new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Crash Alert" };

            _mockCustomProcedureAlertsCommandHandler
                .Setup(h => h.DeleteCustomProcedureAlertsByEntity(alert))
                .ThrowsAsync(new Exception("Unexpected Error"));

            // Act
            var result = await _controller.DeleteCustomProcedureAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomProcedureAlertList_ReturnsBadRequest_WhenListIsNull()
        {
            // Act
            var result = await _controller.UpdateCustomProcedureAlertList(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomProcedureAlertList_ReturnsBadRequest_WhenListIsEmpty()
        {
            // Act
            var result = await _controller.UpdateCustomProcedureAlertList(new List<CustomProcedureAlerts>());

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomProcedureAlertList_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alerts = new List<CustomProcedureAlerts>
    {
        new CustomProcedureAlerts { Id = Guid.NewGuid(), Name = "Alert 1", Description = "Desc 1" }
    };

            _mockCustomProcedureAlertsCommandHandler
                .Setup(h => h.UpdateCustomProcedureAlertsList(alerts))
                .ThrowsAsync(new Exception("Unexpected"));

            // Act
            var result = await _controller.UpdateCustomProcedureAlertList(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }
    }
}
