using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class DataHubService : IDataHubService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<DataHubService> _localizer;
        private readonly ILogger<DataHubService> _logger;
        private readonly string _memberServiceUrl;
        private readonly string _encounterNotesUrl;
        private readonly ITokenService _tokenService;
        private readonly IMemberService _memberService;

        public DataHubService(
            HttpClient httpClient, 
            IConfiguration configuration, 
            IStringLocalizer<DataHubService> localizer, 
            ILogger<DataHubService> logger,
            ITokenService tokenService,
            IMemberService memberService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _tokenService = tokenService;
            _memberService = memberService;
            
            Env.Load();
            _memberServiceUrl = Environment.GetEnvironmentVariable("MemberServiceURL");
            _encounterNotesUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
        }

        public async Task<List<PatientRegistryData>> GetPatientRegistryDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            try
            {
                _logger.LogInformation(_localizer["FetchingPatientRegistryData"], organizationId);

                // Get patients using existing MemberService
                var patients = await _memberService.GetPatientsByOrganizationIdAsync(organizationId, subscription);
                
                var registryData = patients.Select(p => new PatientRegistryData
                {
                    Id = p.Id,
                    FirstName = p.FirstName ?? string.Empty,
                    LastName = p.LastName ?? string.Empty,
                    Email = p.Email ?? string.Empty,
                    PhoneNumber = p.PhoneNumber ?? string.Empty,
                    DateOfBirth = p.DateOfBirth,
                    Gender = p.Gender ?? string.Empty,
                    Address = p.Address ?? string.Empty,
                    City = p.City ?? string.Empty,
                    State = p.State ?? string.Empty,
                    ZipCode = p.ZipCode ?? string.Empty,
                    PrimaryProvider = p.PrimaryProvider ?? string.Empty,
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt,
                    IsActive = p.IsActive,
                    RoleName = p.RoleName ?? string.Empty,
                    OrganizationID = p.OrganizationID,
                    OrganizationName = p.OrganizationName ?? string.Empty
                }).ToList();

                // Apply filters
                registryData = ApplyFilters(registryData, filter);

                _logger.LogInformation(_localizer["PatientRegistryDataFetched"], registryData.Count);
                return registryData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPatientRegistryData"], organizationId);
                throw;
            }
        }

        public async Task<List<PatientRegistryData>> SearchPatientsAsync(string searchTerm, Guid organizationId, bool subscription)
        {
            try
            {
                _logger.LogInformation(_localizer["SearchingPatients"], searchTerm, organizationId);

                var members = await _memberService.SearchMembersAsync(searchTerm, organizationId, subscription);
                
                // Filter only patients
                var patients = members.Where(m => m.RoleName?.Equals("Patient", StringComparison.OrdinalIgnoreCase) == true);
                
                var registryData = patients.Select(p => new PatientRegistryData
                {
                    Id = p.Id,
                    FirstName = p.FirstName ?? string.Empty,
                    LastName = p.LastName ?? string.Empty,
                    Email = p.Email ?? string.Empty,
                    PhoneNumber = p.PhoneNumber ?? string.Empty,
                    DateOfBirth = p.DateOfBirth,
                    Gender = p.Gender ?? string.Empty,
                    Address = p.Address ?? string.Empty,
                    City = p.City ?? string.Empty,
                    State = p.State ?? string.Empty,
                    ZipCode = p.ZipCode ?? string.Empty,
                    PrimaryProvider = p.PrimaryProvider ?? string.Empty,
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt,
                    IsActive = p.IsActive,
                    RoleName = p.RoleName ?? string.Empty,
                    OrganizationID = p.OrganizationID,
                    OrganizationName = p.OrganizationName ?? string.Empty
                }).ToList();

                _logger.LogInformation(_localizer["PatientsSearchCompleted"], registryData.Count);
                return registryData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorSearchingPatients"], searchTerm);
                throw;
            }
        }

        public async Task<int> GetPatientCountAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            try
            {
                var patients = await GetPatientRegistryDataAsync(filter, organizationId, subscription);
                return patients.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorGettingPatientCount"], organizationId);
                throw;
            }
        }

        private List<PatientRegistryData> ApplyFilters(List<PatientRegistryData> data, PatientRegistryFilter filter)
        {
            if (filter == null) return data;

            var filteredData = data.AsQueryable();

            if (!string.IsNullOrEmpty(filter.SearchTerm))
            {
                var searchTerm = filter.SearchTerm.ToLower();
                filteredData = filteredData.Where(p => 
                    p.FirstName.ToLower().Contains(searchTerm) ||
                    p.LastName.ToLower().Contains(searchTerm) ||
                    p.Email.ToLower().Contains(searchTerm) ||
                    p.PhoneNumber.Contains(searchTerm));
            }

            if (!string.IsNullOrEmpty(filter.FirstName))
            {
                filteredData = filteredData.Where(p => p.FirstName.ToLower().Contains(filter.FirstName.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.LastName))
            {
                filteredData = filteredData.Where(p => p.LastName.ToLower().Contains(filter.LastName.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.Email))
            {
                filteredData = filteredData.Where(p => p.Email.ToLower().Contains(filter.Email.ToLower()));
            }

            if (!string.IsNullOrEmpty(filter.Gender))
            {
                filteredData = filteredData.Where(p => p.Gender.Equals(filter.Gender, StringComparison.OrdinalIgnoreCase));
            }

            if (filter.DateOfBirthFrom.HasValue)
            {
                filteredData = filteredData.Where(p => p.DateOfBirth >= filter.DateOfBirthFrom.Value);
            }

            if (filter.DateOfBirthTo.HasValue)
            {
                filteredData = filteredData.Where(p => p.DateOfBirth <= filter.DateOfBirthTo.Value);
            }

            if (filter.IsActive.HasValue)
            {
                filteredData = filteredData.Where(p => p.IsActive == filter.IsActive.Value);
            }

            // Apply sorting
            if (!string.IsNullOrEmpty(filter.SortBy))
            {
                switch (filter.SortBy.ToLower())
                {
                    case "firstname":
                        filteredData = filter.SortDescending ? 
                            filteredData.OrderByDescending(p => p.FirstName) : 
                            filteredData.OrderBy(p => p.FirstName);
                        break;
                    case "lastname":
                        filteredData = filter.SortDescending ? 
                            filteredData.OrderByDescending(p => p.LastName) : 
                            filteredData.OrderBy(p => p.LastName);
                        break;
                    case "email":
                        filteredData = filter.SortDescending ? 
                            filteredData.OrderByDescending(p => p.Email) : 
                            filteredData.OrderBy(p => p.Email);
                        break;
                    case "dateofbirth":
                        filteredData = filter.SortDescending ? 
                            filteredData.OrderByDescending(p => p.DateOfBirth) : 
                            filteredData.OrderBy(p => p.DateOfBirth);
                        break;
                    default:
                        filteredData = filteredData.OrderBy(p => p.LastName).ThenBy(p => p.FirstName);
                        break;
                }
            }
            else
            {
                filteredData = filteredData.OrderBy(p => p.LastName).ThenBy(p => p.FirstName);
            }

            // Apply pagination
            if (filter.PageNumber > 0 && filter.PageSize > 0)
            {
                filteredData = filteredData.Skip((filter.PageNumber - 1) * filter.PageSize).Take(filter.PageSize);
            }

            return filteredData.ToList();
        }

        public async Task<string> ExportPatientRegistryToTextAsync(List<PatientRegistryData> patients)
        {
            try
            {
                var sb = new StringBuilder();
                sb.AppendLine("Patient Registry Export");
                sb.AppendLine($"Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sb.AppendLine($"Total Patients: {patients.Count}");
                sb.AppendLine(new string('-', 80));
                sb.AppendLine();

                foreach (var patient in patients)
                {
                    sb.AppendLine($"Name: {patient.FullName}");
                    sb.AppendLine($"Email: {patient.Email}");
                    sb.AppendLine($"Phone: {patient.PhoneNumber}");
                    sb.AppendLine($"Date of Birth: {patient.DateOfBirth?.ToString("yyyy-MM-dd") ?? "N/A"}");
                    sb.AppendLine($"Age: {patient.Age?.ToString() ?? "N/A"}");
                    sb.AppendLine($"Gender: {patient.Gender}");
                    sb.AppendLine($"Address: {patient.Address}, {patient.City}, {patient.State} {patient.ZipCode}");
                    sb.AppendLine($"Primary Provider: {patient.PrimaryProvider}");
                    sb.AppendLine($"Active: {(patient.IsActive ? "Yes" : "No")}");
                    sb.AppendLine(new string('-', 40));
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorExportingToText"]);
                throw;
            }
        }

        public async Task<byte[]> ExportPatientRegistryToCsvAsync(List<PatientRegistryData> patients)
        {
            try
            {
                var sb = new StringBuilder();
                
                // CSV Header
                sb.AppendLine("First Name,Last Name,Email,Phone,Date of Birth,Age,Gender,Address,City,State,Zip Code,Primary Provider,Active,Created At,Updated At");

                // CSV Data
                foreach (var patient in patients)
                {
                    sb.AppendLine($"\"{patient.FirstName}\",\"{patient.LastName}\",\"{patient.Email}\",\"{patient.PhoneNumber}\"," +
                                 $"\"{patient.DateOfBirth?.ToString("yyyy-MM-dd") ?? ""}\",\"{patient.Age?.ToString() ?? ""}\",\"{patient.Gender}\"," +
                                 $"\"{patient.Address}\",\"{patient.City}\",\"{patient.State}\",\"{patient.ZipCode}\"," +
                                 $"\"{patient.PrimaryProvider}\",\"{(patient.IsActive ? "Yes" : "No")}\"," +
                                 $"\"{patient.CreatedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""}\",\"{patient.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""}\"");
                }

                return Encoding.UTF8.GetBytes(sb.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorExportingToCsv"]);
                throw;
            }
        }

        // Query Operations
        public async Task<List<PatientRegistryData>> ExecuteQueryAsync(DataHubQueryOperation queryOperation, Guid organizationId, bool subscription)
        {
            try
            {
                _logger.LogInformation(_localizer["ExecutingQuery"], queryOperation.Name, organizationId);

                var filter = JsonSerializer.Deserialize<PatientRegistryFilter>(queryOperation.QueryJson) ?? new PatientRegistryFilter();

                switch (queryOperation.OperationType)
                {
                    case QueryOperationType.RunNew:
                        return await GetPatientRegistryDataAsync(filter, organizationId, subscription);

                    case QueryOperationType.RunSubset:
                    case QueryOperationType.RunSubsetNot:
                        // For subset operations, we would need additional logic to handle existing result sets
                        // For now, treating as RunNew
                        return await GetPatientRegistryDataAsync(filter, organizationId, subscription);

                    default:
                        return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorExecutingQuery"], queryOperation.Name);
                throw;
            }
        }

        public async Task<SavedQuery> SaveQueryAsync(SavedQuery query)
        {
            try
            {
                _logger.LogInformation(_localizer["SavingQuery"], query.Name);

                // For now, we'll store in memory or local storage
                // In a real implementation, this would save to database
                query.Id = Guid.NewGuid();
                query.CreatedAt = DateTime.UtcNow;

                return query;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorSavingQuery"], query.Name);
                throw;
            }
        }

        public async Task<List<SavedQuery>> GetSavedQueriesAsync(Guid organizationId, Guid? userId = null)
        {
            try
            {
                _logger.LogInformation(_localizer["FetchingSavedQueries"], organizationId);

                // For now, returning empty list
                // In a real implementation, this would fetch from database
                return new List<SavedQuery>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingSavedQueries"], organizationId);
                throw;
            }
        }

        public async Task<SavedQuery> GetSavedQueryByIdAsync(Guid queryId)
        {
            try
            {
                _logger.LogInformation(_localizer["FetchingSavedQuery"], queryId);

                // For now, returning null
                // In a real implementation, this would fetch from database
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingSavedQuery"], queryId);
                throw;
            }
        }

        public async Task DeleteSavedQueryAsync(Guid queryId)
        {
            try
            {
                _logger.LogInformation(_localizer["DeletingSavedQuery"], queryId);

                // For now, no-op
                // In a real implementation, this would delete from database
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingSavedQuery"], queryId);
                throw;
            }
        }

        // Registry Tab Operations
        public async Task<List<PatientRegistryData>> GetDemographicsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Demographics";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetVitalsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Vitals";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetLabsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Labs";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetDiagnosticImagingDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "DiagnosticImaging";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetProceduresDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Procedures";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetICDDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "ICD";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetCPTDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "CPT";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetRxDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Rx";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetImmunizationDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Immunization";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetEncountersDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Encounters";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetAllergiesDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Allergies";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetMedicalHistoryDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "MedicalHistory";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetStructuredDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "StructuredData";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetReferralsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Referrals";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }

        public async Task<List<PatientRegistryData>> GetReportsDataAsync(PatientRegistryFilter filter, Guid organizationId, bool subscription)
        {
            filter.RegistryTab = "Reports";
            return await GetPatientRegistryDataAsync(filter, organizationId, subscription);
        }
    }
