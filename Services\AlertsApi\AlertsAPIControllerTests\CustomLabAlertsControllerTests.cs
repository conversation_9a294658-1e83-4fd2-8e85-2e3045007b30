using AlertsApi.Controllers;
using AlertsBusinessLayer;
using AlertsContracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIControllerTests
{
    [TestFixture]
    public class CustomLabAlertsControllerTests
    {
        private Mock<ICustomLabAlertsCommandHandler<CustomLabAlerts>> _mockCustomLabAlertsCommandHandler;
        private Mock<ICustomLabAlertsQueryHandler<CustomLabAlerts>> _mockCustomLabAlertsQueryHandler;
        private Mock<ILogger<CustomLabAlertsController>> _mockLogger;
        private Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>> _mockLocalizer;
        private CustomLabAlertsController _controller;

        [SetUp]
        public void Setup()
        {
            _mockCustomLabAlertsCommandHandler = new Mock<ICustomLabAlertsCommandHandler<CustomLabAlerts>>();
            _mockCustomLabAlertsQueryHandler = new Mock<ICustomLabAlertsQueryHandler<CustomLabAlerts>>();
            _mockLogger = new Mock<ILogger<CustomLabAlertsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>>();
            
            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));
            _mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()]).Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));
            
            _controller = new CustomLabAlertsController(
                _mockCustomLabAlertsCommandHandler.Object,
                _mockCustomLabAlertsQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        

        [Test]
        public async Task GetAllById_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var Id = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockCustomLabAlertsQueryHandler.Setup(h => h.GetCustomLabAlertsById(Id)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllById(Id);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsOkResult_WithActiveAlerts()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<CustomLabAlerts>
            {
                new CustomLabAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "Lab Alert 1", Description = "Description 1", IsActive = true },
                new CustomLabAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "Lab Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockCustomLabAlertsQueryHandler.Setup(h => h.GetActiveCustomLabAlertsByOrganizationId(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.Value, Is.EqualTo(alerts));
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockCustomLabAlertsQueryHandler.Setup(h => h.GetActiveCustomLabAlertsByOrganizationId(organizationId)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllByIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddCustomLabAlert_ReturnsOkResult_WhenAlertsAdded()
        {
            // Arrange
            var alerts = new List<CustomLabAlerts>
            {
                new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Lab Alert 1", Description = "Description 1" },
                new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Lab Alert 2", Description = "Description 2" }
            };

            _mockCustomLabAlertsCommandHandler.Setup(h => h.AddCustomLabAlerts(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddCustomLabAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task AddCustomLabAlert_ReturnsBadRequest_WhenNoAlertsProvided()
        {
            // Act
            var result = await _controller.AddCustomLabAlert(new List<CustomLabAlerts>());

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddCustomLabAlert_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alerts = new List<CustomLabAlerts>
            {
                new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Lab Alert 1", Description = "Description 1" }
            };

            var exceptionMessage = "Database error";
            _mockCustomLabAlertsCommandHandler.Setup(h => h.AddCustomLabAlerts(alerts)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.AddCustomLabAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomLabAlert_ReturnsOkResult_WhenAlertUpdated()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomLabAlerts { Id = alertId, Name = "Updated Lab Alert", Description = "Updated Description" };

            _mockCustomLabAlertsCommandHandler.Setup(h => h.UpdateCustomLabAlerts(alert)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateCustomLabAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task UpdateCustomLabAlert_ReturnsBadRequest_WhenAlertIdMismatch()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Updated Lab Alert", Description = "Updated Description" };

            // Act
            var result = await _controller.UpdateCustomLabAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomLabAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomLabAlerts { Id = alertId, Name = "Updated Lab Alert", Description = "Updated Description" };

            _mockCustomLabAlertsCommandHandler.Setup(h => h.UpdateCustomLabAlerts(alert)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateCustomLabAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task UpdateCustomLabAlertList_ReturnsOkResult_WhenAlertsUpdated()
        {
            // Arrange
            var alerts = new List<CustomLabAlerts>
            {
                new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Updated Lab Alert 1", Description = "Updated Description 1" },
                new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Updated Lab Alert 2", Description = "Updated Description 2" }
            };

            _mockCustomLabAlertsCommandHandler.Setup(h => h.UpdateCustomLabAlertsList(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateCustomLabAlertList(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomLabAlert_ReturnsOkResult_WhenAlertDeleted()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomLabAlertsCommandHandler.Setup(h => h.DeleteCustomLabAlertsById(alertId)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteCustomLabAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomLabAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomLabAlertsCommandHandler.Setup(h => h.DeleteCustomLabAlertsById(alertId)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteCustomLabAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteCustomLabAlertByEntity_ReturnsBadRequest_WhenAlertIsNull()
        {
            // Act
            var result = await _controller.DeleteCustomLabAlertByEntity(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task DeleteCustomLabAlertByEntity_ReturnsOkResult_WhenDeletedSuccessfully()
        {
            // Arrange
            var alert = new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Test Alert" };

            _mockCustomLabAlertsCommandHandler
                .Setup(h => h.DeleteCustomLabAlertsByEntity(alert))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteCustomLabAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomLabAlertByEntity_ReturnsNotFound_WhenAlertNotFound()
        {
            // Arrange
            var alert = new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Missing Alert" };

            _mockCustomLabAlertsCommandHandler
                .Setup(h => h.DeleteCustomLabAlertsByEntity(alert))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteCustomLabAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteCustomLabAlertByEntity_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alert = new CustomLabAlerts { Id = Guid.NewGuid(), Name = "Crash Alert" };

            _mockCustomLabAlertsCommandHandler
                .Setup(h => h.DeleteCustomLabAlertsByEntity(alert))
                .ThrowsAsync(new Exception("Unhandled"));

            // Act
            var result = await _controller.DeleteCustomLabAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }
    }
}
