﻿CREATE TABLE [EncounterNotesService].[PreventiveMedicines] (
    [PreventiveMedicineId] UNIQUEIDENTIFIER NOT NULL,
    [PatientId]            UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId]       UNIQUEIDENTIFIER NOT NULL,
    [PCPId]                UNIQUEIDENTIFIER NOT NULL,
    [CreatedDate]          DATETIME2 (7)    NOT NULL,
    [UpdatedDate]          DATETIME2 (7)    NOT NULL,
    [IsActive]             BIT              NOT NULL,
    [Category]             NVARCHAR (MAX)   NULL,
    [SubCategory]          NVARCHAR (MAX)   NULL,
    [Symptoms]             NVARCHAR (MAX)   NULL,
    [Detection]            NVARCHAR (MAX)   NULL,
    [Notes]                NVARCHAR (MAX)   NULL,
    [Subscription]         BIT              DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_PreventiveMedicines] PRIMARY KEY CLUSTERED ([PreventiveMedicineId] ASC)
);

