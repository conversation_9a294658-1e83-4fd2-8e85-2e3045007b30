using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Professional cosigning component for document signature workflow
    /// Designed with Nabla-inspired UI/UX principles
    /// </summary>
    public partial class CosigningComponent : ComponentBase
    {
        [Inject] private ICosigningService CosigningService { get; set; } = default!;
        [Inject] private IStringLocalizer<TeyaAIScribeResource> Localizer { get; set; } = default!;
        [Inject] private ILogger<CosigningComponent> Logger { get; set; } = default!;
        [Inject] private ISnackbar Snackbar { get; set; } = default!;

        /// <summary>
        /// The record ID for which cosigning is being performed
        /// </summary>
        [Parameter] public Guid RecordId { get; set; }

        /// <summary>
        /// The patient ID associated with the record
        /// </summary>
        [Parameter] public Guid PatientId { get; set; }

        /// <summary>
        /// The patient name to display in the component
        /// </summary>
        [Parameter] public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// The organization ID for the cosigning workflow
        /// </summary>
        [Parameter] public Guid OrganizationId { get; set; }

        /// <summary>
        /// Whether to show the cosigning section
        /// </summary>
        [Parameter] public bool ShowCosigningSection { get; set; } = true;

        /// <summary>
        /// Whether the document requires a cosignature
        /// </summary>
        [Parameter] public bool RequiresCosignature { get; set; } = false;

        /// <summary>
        /// Event callback fired when signature is updated
        /// </summary>
        [Parameter] public EventCallback<Cosigning> OnSignatureUpdated { get; set; }

        /// <summary>
        /// Current cosigning state
        /// </summary>
        private Cosigning CurrentCosigning { get; set; } = new();

        /// <summary>
        /// Whether a signing operation is in progress
        /// </summary>
        private bool IsProcessing { get; set; } = false;

        /// <summary>
        /// Initialize the component and load cosigning status
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadCosigningStatus();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing cosigning component for record {RecordId}", RecordId);
            }
        }

        /// <summary>
        /// Handle parameter changes and reload cosigning status if needed
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            if (RecordId != Guid.Empty)
            {
                await LoadCosigningStatus();
            }
        }

        /// <summary>
        /// Load the current cosigning status from the backend
        /// </summary>
        private async Task LoadCosigningStatus()
        {
            try
            {
                Logger.LogInformation("Loading cosigning status for record {RecordId}", RecordId);
                
                var cosignings = await CosigningService.GetCosigningsByRecordId(RecordId);
                CurrentCosigning = cosignings?.OrderByDescending(c => c.Date).FirstOrDefault() ?? new Cosigning
                {
                    Id = Guid.NewGuid(),
                    RecordId = RecordId,
                    OrganizationId = OrganizationId,
                    IsSigned = false,
                    IsCosigned = false,
                    IsLocked = false
                };

                Logger.LogInformation("Cosigning status loaded successfully for record {RecordId}. IsSigned: {IsSigned}, IsCosigned: {IsCosigned}", 
                    RecordId, CurrentCosigning.IsSigned, CurrentCosigning.IsCosigned);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning status for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLoadingSignatureStatus"], Severity.Error);
                
                // Initialize with default values on error
                CurrentCosigning = new Cosigning
                {
                    Id = Guid.NewGuid(),
                    RecordId = RecordId,
                    OrganizationId = OrganizationId,
                    IsSigned = false,
                    IsCosigned = false,
                    IsLocked = false
                };
            }
        }

        /// <summary>
        /// Handle primary document signing
        /// </summary>
        private async Task SignDocument()
        {
            if (IsProcessing) return;

            IsProcessing = true;
            try
            {
                Logger.LogInformation("Signing document for record {RecordId}", RecordId);

                CurrentCosigning.IsSigned = true;
                CurrentCosigning.SignerId = Guid.NewGuid(); // TODO: Get from current user context
                CurrentCosigning.SignerName = "Current User"; // TODO: Get from current user context
                CurrentCosigning.Date = DateTime.UtcNow;

                if (CurrentCosigning.Id == Guid.Empty)
                {
                    CurrentCosigning.Id = Guid.NewGuid();
                    await CosigningService.AddCosigning(new List<Cosigning> { CurrentCosigning });
                }
                else
                {
                    await CosigningService.UpdateCosigning(CurrentCosigning);
                }
                
                Logger.LogInformation("Document signed successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentSignedSuccessfully"], Severity.Success);
                
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error signing document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorSigningDocument"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Handle document cosigning
        /// </summary>
        private async Task CosignDocument()
        {
            if (IsProcessing) return;

            IsProcessing = true;
            try
            {
                Logger.LogInformation("Cosigning document for record {RecordId}", RecordId);

                CurrentCosigning.IsCosigned = true;
                CurrentCosigning.CosignerId = Guid.NewGuid(); // TODO: Get from current user context
                CurrentCosigning.CosignerName = "Supervising Provider"; // TODO: Get from current user context
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                await CosigningService.UpdateCosigning(CurrentCosigning);
                
                Logger.LogInformation("Document cosigned successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentCosignedSuccessfully"], Severity.Success);
                
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error cosigning document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorCosigningDocument"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Get the appropriate color for the status chip
        /// </summary>
        private Color GetStatusColor()
        {
            if (CurrentCosigning.IsLocked) return Color.Warning;
            if (CurrentCosigning.IsCosigned) return Color.Success;
            if (CurrentCosigning.IsSigned) return Color.Info;
            return Color.Default;
        }

        /// <summary>
        /// Get the status text for the status chip
        /// </summary>
        private string GetStatusText()
        {
            if (CurrentCosigning.IsLocked) return Localizer["Locked"];
            if (CurrentCosigning.IsCosigned) return Localizer["Cosigned"];
            if (CurrentCosigning.IsSigned) return Localizer["Signed"];
            return Localizer["PendingSignature"];
        }

        /// <summary>
        /// Public method to refresh the signature status
        /// Can be called from parent components
        /// </summary>
        public async Task RefreshSignatureStatus()
        {
            await LoadCosigningStatus();
            StateHasChanged();
        }

        /// <summary>
        /// Check if the component should be visible based on current state
        /// </summary>
        public bool ShouldShowComponent()
        {
            return ShowCosigningSection && RecordId != Guid.Empty;
        }

        /// <summary>
        /// Get the current signature completion percentage for progress indicators
        /// </summary>
        public int GetSignatureProgress()
        {
            if (CurrentCosigning.IsLocked) return 100;
            if (CurrentCosigning.IsCosigned) return 100;
            if (CurrentCosigning.IsSigned && !RequiresCosignature) return 100;
            if (CurrentCosigning.IsSigned && RequiresCosignature) return 75;
            return 0;
        }
    }
}
