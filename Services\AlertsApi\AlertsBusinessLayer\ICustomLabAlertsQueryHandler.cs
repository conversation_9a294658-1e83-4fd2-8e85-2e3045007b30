using AlertsContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AlertsBusinessLayer
{
    public interface ICustomLabAlertsQueryHandler<T>
    {
        Task<IEnumerable<CustomLabAlerts>> GetAllCustomLabAlerts();
        Task<CustomLabAlerts> GetCustomLabAlertsById(Guid id);
        //Task<IEnumerable<CustomLabAlerts>> GetCustomLabAlertsByPatientId(Guid patientId);
        Task<IEnumerable<CustomLabAlerts>> GetActiveCustomLabAlertsByOrganizationId(Guid organizationId);
    }
}
