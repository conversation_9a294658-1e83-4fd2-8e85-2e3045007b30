﻿@page "/CosigningPage"
@using Microsoft.AspNetCore.Authorization
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using TeyaUIViewModels.ViewModel
@using System
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject IMemberService MemberService
@inject ILogger<CosigningPage> Logger
@inject IDialogService DialogService



<div style="position: relative;">
    <MudTextField Variant="Variant.Outlined"
                  Label="Signed/Cosigned Progress Notes"
                  Lines="5"
                  @bind-Value="@richTextContent"
                  ReadOnly="true"
                  FullWidth="true"
                  InputType="InputType.Text"
                  Multiline="true"
                  Style="max-height: 400px; height: 100%; overflow-y: auto; white-space: pre-wrap;
                         font-family: monospace; margin-bottom: 10px;" />
    <div style="position: absolute; top: 8px; right: 8px;">
        <MudButton Size="Size.Small"
                   OnClick="OpenNewDialogBox"
                   Disabled="_isLocked"
                   Style="background-color: #f5f5f5; border: 1px solid #e0e0e0;">
            Sign
        </MudButton>

    </div>
</div>



<MudDialog @ref="_dialog" Style="width: 95vw; max-width: 1400px; height: 90vh;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%; padding: 16px;">
            <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
                @Localizer["Signed/Cosigned Progress Notes"]
            </MudText>
            <MudIconButton Icon="@Icons.Material.Filled.Close"
                           Size="Size.Small"
                           OnClick="CancelData"
                           Style="margin: -4px;" />
        </div>
    </TitleContent>

    <DialogContent>
        <div style="height: calc(90vh - 150px); display: flex; padding: 0; overflow: hidden;">
            <!-- Left Section -->
            <div style="flex: 0 0 60%; padding: 16px; border-right: 1px solid #e0e0e0; overflow-y: auto;">
                <div style="margin-bottom: 24px;">
                    <div style="background: #f5f5f5; padding: 16px; border-radius: 4px;">
                        <MudText Typo="Typo.body1" Class="mb-2">@Localizer["Patient Name"]: @patient</MudText>

                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <MudText Typo="Typo.subtitle1" Class="mb-2">@Localizer["Notes"]</MudText>
                    <MudTextField Variant="Variant.Outlined"
                                  Lines="20"
                                  ReadOnly="true"
                                  @bind-Value="@patientNotes"
                                  FullWidth="true"
                                  Class="mb-4"
                                  InputType="InputType.Text"
                                  Multiline="true"
                                  Style="max-height: 400px; height: 100%; overflow-y: auto; white-space: pre-wrap; font-family: monospace;" />
                </div>
            </div>

            <!-- Right Section -->
            <div style="flex: 1; padding: 16px; overflow-y: auto;">
                <MudText Typo="Typo.h6" Class="mb-4">@Localizer["Assign To Progress Notes"]</MudText>

                <div style="display: flex; gap: 16px; margin-bottom: 24px;">
                    <!-- From dropdown (Patient's PCP) -->
                    <MudSelect @bind-Value="pcpName"
                               Label="From"
                               Variant="Variant.Outlined"
                               Size="Size.Small"
                               Style="flex: 1;"
                               Disabled="true">
                        <MudSelectItem Value="@pcpName">@pcpName</MudSelectItem>
                    </MudSelect>

                    <!-- To dropdown (Only visible for Co-Sign) -->
                    @if (Dense_Radio)
                    {
                        <MudSelect @bind-Value="_selectedToProvider"
                                   Label="To"
                                   Variant="Variant.Outlined"
                                   Size="Size.Small"
                                   Style="flex: 1;">
                            @foreach (var provider in ProviderList)
                            {
                                <MudSelectItem Value="@provider">@provider</MudSelectItem>
                            }
                        </MudSelect>
                    }
                </div>

                <div class="mb-4 d-flex align-items-center">
                    <MudText Typo="Typo.body1" Class="me-2">@Localizer["Status:"]</MudText>
                    <MudRadioGroup @bind-Value="Dense_Radio" Class="d-flex gap-3">
                        <MudRadio Value="true" Color="Color.Primary" Dense="true">@Localizer["Co-Sign"]</MudRadio>
                        <MudRadio Value="false" Color="Color.Primary" Dense="true">@Localizer["Sign"]</MudRadio>
                    </MudRadioGroup>
                </div>


               <div class="d-flex gap-2 mb-4">
    <MudButton Class="tiny-button"
               Variant="Variant.Filled"
               Color="Color.Primary"
               StartIcon="@Icons.Material.Filled.FolderOpen">
        @Localizer["Browse"]
    </MudButton>
    <MudButton Class="tiny-button"
               Variant="Variant.Filled"
               Color="Color.Primary"
               StartIcon="@Icons.Material.Filled.AccessTime">
        @Localizer["Time Stamp"]
    </MudButton>
    <MudButton Class="tiny-button"
               Variant="Variant.Filled"
               Color="Color.Primary"
               StartIcon="@Icons.Material.Filled.Spellcheck">
        @Localizer["Check Spelling"]
    </MudButton>
</div>



                <div class="d-flex flex-column gap-3">
                    @foreach (var category in new[] { "HPI", "Immunization", "Treatment", "Documentations" })
                    {
                        <div class="d-flex align-center justify-space-between">
                            <MudText Typo="Typo.body1">@Localizer[category]</MudText>
                            <MudRating />
                        </div>
                    }
                </div>
            </div>
        </div>
    </DialogContent>

    <DialogActions>
        <div class="d-flex justify-end gap-2 pa-4" style="border-top: 1px solid #e0e0e0; width: 100%;">
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Secondary"
                       OnClick="CancelData"
                       Dense="true"
                       Style="min-width: 120px;">
                @Localizer["Cancel"]
            </MudButton>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       OnClick="HandleOk"
                       Disabled="@_isLocked"
                       Dense="true"
                       Style="min-width: 120px;">
                @Localizer["OK"]
            </MudButton>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       Disabled="@_isLocked"
                       OnClick="HandleFinalize"
                       Dense="true"
                       Style="min-width: 120px;">
                @Localizer["Lock"]
            </MudButton>
        </div>
    </DialogActions>
</MudDialog>

<style>
    .tiny-button {
        padding: 4px 10px !important;
        font-size: 0.75rem !important;
        min-height: 32px !important;
        line-height: 1.2 !important;
    }

        .tiny-button .mud-icon {
            font-size: 18px !important;
        }
</style>

@code {
    private MudDialog? _dialog;
    private bool _isLocked = false;
    private bool Dense_Radio = false;
    private string richTextContent = string.Empty;
    private string patient = "Sample Patient";
    private string patientNotes = "Sample patient notes content...";
    private string pcpName = "Dr. Sample PCP";
    private string _selectedToProvider = string.Empty;
    private List<string> ProviderList = new List<string> { "Dr. Provider 1", "Dr. Provider 2", "Dr. Provider 3" };

    protected override async Task OnInitializedAsync()
    {
        // Initialize component
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            // Load any required data
            Logger.LogInformation("Loading cosigning page data");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading cosigning page data");
        }
    }

    private async Task OpenNewDialogBox()
    {
        if (_dialog != null)
        {
            await _dialog.ShowAsync();
        }
    }

    private async Task CancelData()
    {
        if (_dialog != null)
        {
            await _dialog.CloseAsync();
        }
    }

    private async Task HandleOk()
    {
        try
        {
            // Handle OK button click
            Logger.LogInformation("Handling OK button click");

            if (_dialog != null)
            {
                await _dialog.CloseAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling OK button click");
        }
    }

    private async Task HandleFinalize()
    {
        try
        {
            // Handle finalize/lock button click
            Logger.LogInformation("Handling finalize button click");
            _isLocked = true;

            if (_dialog != null)
            {
                await _dialog.CloseAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling finalize button click");
        }
    }

    private void HandleBackdropClick()
    {
        // Handle backdrop click if needed
    }
}