using System;
using System.Collections.Generic;

namespace TeyaUIModels.Model
{
    public enum QueryOperationType
    {
        RunNew,
        RunSubset,
        RunSubsetNot,
        SaveQuery
    }

    public class DataHubQueryOperation
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public QueryOperationType OperationType { get; set; }
        public PatientRegistryFilter Filter { get; set; } = new();
        public string QueryJson { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid OrganizationId { get; set; }
        public bool IsActive { get; set; } = true;
        public int ResultCount { get; set; }
        public DateTime? LastExecuted { get; set; }
    }

    public class SavedQuery
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string QueryJson { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public Guid OrganizationId { get; set; }
        public bool IsShared { get; set; } = false;
        public int TimesUsed { get; set; } = 0;
        public DateTime? LastUsed { get; set; }
    }
}
