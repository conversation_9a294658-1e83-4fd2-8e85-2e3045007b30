using AlertsBusinessLayer;
using AlertsBusinessLayer.QueryHandler;
using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIBussinessLayerTestCases
{
    [TestFixture]
    public class CustomImmunizationAlertsQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<CustomImmunizationAlertsQueryHandler>> _mockLogger;
        private Mock<IStringLocalizer<CustomImmunizationAlertsQueryHandler>> _mockLocalizer;
        private Mock<ICustomImmunizationAlertsRepository> _mockCustomImmunizationAlertsRepository;
        private CustomImmunizationAlertsQueryHandler _handler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<CustomImmunizationAlertsQueryHandler>>();
            _mockLocalizer = new Mock<IStringLocalizer<CustomImmunizationAlertsQueryHandler>>();
            _mockCustomImmunizationAlertsRepository = new Mock<ICustomImmunizationAlertsRepository>();

            _mockUnitOfWork.Setup(u => u.CustomImmunizationAlertsRepository).Returns(_mockCustomImmunizationAlertsRepository.Object);

            _handler = new CustomImmunizationAlertsQueryHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllCustomImmunizationAlerts_ShouldReturnAllAlerts()
        {
            // Arrange
            var alerts = new List<CustomImmunizationAlerts>
            {
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Immunization Alert 1", Description = "Description 1" },
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Immunization Alert 2", Description = "Description 2" }
            };

            _mockCustomImmunizationAlertsRepository.Setup(r => r.GetAllCustomImmunizationAlertsAsync()).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetAllCustomImmunizationAlerts();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(alerts));
        }

        [Test]
        public async Task GetAllCustomImmunizationAlerts_WhenExceptionOccurs_ShouldPropagateException()
        {
            // Arrange
            var exceptionMessage = "Database error";
            _mockCustomImmunizationAlertsRepository.Setup(r => r.GetAllCustomImmunizationAlertsAsync())
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetAllCustomImmunizationAlerts());
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }

        [Test]
        public async Task GetCustomImmunizationAlertsById_WithExistingId_ShouldReturnAlert()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomImmunizationAlerts { Id = alertId, Name = "Immunization Alert 1", Description = "Description 1" };

            _mockCustomImmunizationAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync(alert);

            // Act
            var result = await _handler.GetCustomImmunizationAlertsById(alertId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(alertId));
            Assert.That(result.Name, Is.EqualTo("Immunization Alert 1"));
            Assert.That(result.Description, Is.EqualTo("Description 1"));
        }

        [Test]
        public void GetCustomImmunizationAlertsById_WithNonExistentId_ShouldThrowKeyNotFoundException()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomImmunizationAlertsRepository.Setup(r => r.GetByIdAsync(alertId)).ReturnsAsync((CustomImmunizationAlerts)null);

            // Act & Assert
            Assert.ThrowsAsync<KeyNotFoundException>(() => _handler.GetCustomImmunizationAlertsById(alertId));
        }

        [Test]
        public async Task GetCustomImmunizationAlertsById_ShouldReturnAlertForPatient()
        {
            // Arrange
            var Id = Guid.NewGuid();
            var alert = new CustomImmunizationAlerts
            {
                Id = Id,
                Name = "Immunization Alert",
                Description = "Description"
            };

            _mockCustomImmunizationAlertsRepository
                .Setup(r => r.GetByIdAsync(Id))
                .ReturnsAsync(alert);

            // Act
            var result = await _handler.GetCustomImmunizationAlertsById(Id);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(Id));
        }


        [Test]
        public async Task GetActiveCustomImmunizationAlertsByOrganizationId_ShouldReturnActiveAlertsForPatient()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<CustomImmunizationAlerts>
            {
                new CustomImmunizationAlerts { Id = Guid.NewGuid(),OrganizationId = organizationId, Name = "Immunization Alert 1", Description = "Description 1", IsActive = true },
                new CustomImmunizationAlerts { Id = Guid.NewGuid(),OrganizationId = organizationId, Name = "Immunization Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockCustomImmunizationAlertsRepository.Setup(r => r.GetActiveCustomImmunizationAlertsByOrganizationIdAsync(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _handler.GetActiveCustomImmunizationAlertsByOrganizationId(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.All(a => a.OrganizationId == organizationId && a.IsActive), Is.True);
        }

          




        [Test]
        public void GetActiveCustomImmunizationAlertsByPatientId_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var exceptionMessage = "Database connection error";

            _mockCustomImmunizationAlertsRepository.Setup(r => r.GetActiveCustomImmunizationAlertsByOrganizationIdAsync(organizationId))
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act & Assert
            var exception = Assert.ThrowsAsync<Exception>(() => _handler.GetActiveCustomImmunizationAlertsByOrganizationId(organizationId));
            Assert.That(exception.InnerException.Message, Is.EqualTo(exceptionMessage));
        }
    }
}
