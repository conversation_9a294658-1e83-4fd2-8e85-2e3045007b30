﻿CREATE TABLE [ProviderBilling].[DentalClaims] (
    [Id]                                     UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [ClaimNumber]                            VARCHAR (30)     NULL,
    [PatientInfo]                            VARCHAR (MAX)    NULL,
    [FacilityInfo]                           VARCHAR (30)     NULL,
    [OrthodonticsTreatmentServiceCommenced ] VARCHAR (30)     NULL,
    [OrthodonticsTreatmentDate]              DATE             NULL,
    [OrthodonticsTreatmentRemaining]         VARCHAR (30)     NULL,
    [ClaimDate]                              DATE             NULL,
    [ServiceDate]                            DATE             NULL,
    [DentistPretreatmentEstimates]           BIT              NOT NULL,
    [DentistStatementOfActualServices]       BIT              NOT NULL,
    [MedicaidClaim]                          BIT              NOT NULL,
    [EPSDT]                                  BIT              NOT NULL,
    [PriorAuthorizationNumber]               VARCHAR (20)     NULL,
    [OrganizationId]                         UNIQUEIDENTIFIER NULL,
    [Subscription]                           BIT              NULL,
    [IsOccupationalInjury]                   BIT              NOT NULL,
    [OccupationalInjuryDetails]              VARCHAR (50)     NULL,
    [IsAutoAccident]                         BIT              NOT NULL,
    [AutoAccidentDetails]                    VARCHAR (50)     NULL,
    [IsOtherAccident]                        BIT              NOT NULL,
    [OtherAccidentDetails]                   VARCHAR (50)     NULL,
    [IsProsthesis]                           BIT              NOT NULL,
    [DentistInfo]                            VARCHAR (30)     NULL,
    [ReplacementReason]                      VARCHAR (30)     NULL,
    [PriorDate]                              DATE             NULL,
    [OrthodonticsTreatment]                  BIT              NOT NULL,
    [Status]                                 VARCHAR (30)     NULL,
    [PlaceOfTreatment]                       VARCHAR (30)     NULL,
    [HasRadiographs]                         BIT              NOT NULL,
    [RadiographsDetails]                     VARCHAR (50)     NULL,
    [BillToPatient]                          BIT              NOT NULL,
    [Copay]                                  DECIMAL (18, 2)  NOT NULL,
    [PatientUncoveredAmount]                 DECIMAL (18, 2)  NOT NULL,
    [PatientCharges]                         DECIMAL (18, 2)  NOT NULL,
    [PatientPayments]                        DECIMAL (18, 2)  NOT NULL,
    [PatientBalance]                         DECIMAL (18, 2)  NOT NULL,
    [TotalCharges]                           DECIMAL (18, 2)  NOT NULL,
    [TotalPayments]                          DECIMAL (18, 2)  NOT NULL,
    [TotalBalance]                           DECIMAL (18, 2)  NOT NULL,
    [IsActive]                               BIT              NOT NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC)
);

