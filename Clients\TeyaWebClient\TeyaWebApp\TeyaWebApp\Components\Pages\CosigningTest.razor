@page "/cosigning-test"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging

<PageTitle>Cosigning Component Test</PageTitle>

<div class="container-fluid">
    <MudText Typo="Typo.h4" Class="mb-4">Cosigning Component Test</MudText>
    
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h6" Class="mb-2">Test Cosigning Component</MudText>
        <MudText Typo="Typo.body2" Class="mb-4">
            This page demonstrates the professional cosigning component with Nabla-inspired design.
        </MudText>
        
        <!-- Test Cosigning Component -->
        <CosigningComponent RecordId="@testRecordId"
                          PatientId="@testPatientId"
                          PatientName="@testPatientName"
                          OrganizationId="@testOrganizationId"
                          ShowCosigningSection="true"
                          RequiresCosignature="@requiresCosignature"
                          OnSignatureUpdated="@OnSignatureUpdated" />
        
        <!-- Test Controls -->
        <MudDivider Class="my-4" />
        <MudText Typo="Typo.h6" Class="mb-2">Test Controls</MudText>
        
        <div class="d-flex gap-2 mb-2">
            <MudButton Variant="Variant.Outlined" 
                       Color="Color.Primary"
                       OnClick="@(() => requiresCosignature = !requiresCosignature)">
                Toggle Cosignature Requirement: @(requiresCosignature ? "Required" : "Not Required")
            </MudButton>
            
            <MudButton Variant="Variant.Outlined" 
                       Color="Color.Secondary"
                       OnClick="@GenerateNewTestRecord">
                Generate New Test Record
            </MudButton>
        </div>
        
        <!-- Status Display -->
        <MudAlert Severity="Severity.Info" Class="mt-4">
            <strong>Current Test Record:</strong><br/>
            Record ID: @testRecordId<br/>
            Patient: @testPatientName<br/>
            Requires Cosignature: @requiresCosignature
        </MudAlert>
        
        @if (!string.IsNullOrEmpty(lastSignatureUpdate))
        {
            <MudAlert Severity="Severity.Success" Class="mt-2">
                <strong>Last Signature Update:</strong><br/>
                @lastSignatureUpdate
            </MudAlert>
        }
    </MudPaper>
</div>

@code {
    private Guid testRecordId = Guid.NewGuid();
    private Guid testPatientId = Guid.NewGuid();
    private Guid testOrganizationId = Guid.NewGuid();
    private string testPatientName = "John Doe";
    private bool requiresCosignature = false;
    private string lastSignatureUpdate = string.Empty;

    protected override void OnInitialized()
    {
        // Initialize with test data
        GenerateNewTestRecord();
    }

    private void GenerateNewTestRecord()
    {
        testRecordId = Guid.NewGuid();
        testPatientId = Guid.NewGuid();
        testPatientName = $"Test Patient {Random.Shared.Next(1000, 9999)}";
        lastSignatureUpdate = string.Empty;
        StateHasChanged();
    }

    private async Task OnSignatureUpdated(Cosigning cosigning)
    {
        lastSignatureUpdate = $"Signature updated at {DateTime.Now:HH:mm:ss} - " +
                             $"Signed: {cosigning.IsSigned}, Cosigned: {cosigning.IsCosigned}";
        StateHasChanged();
    }
}
