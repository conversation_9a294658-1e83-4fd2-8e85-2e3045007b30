using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsBusinessLayer.QueryFolder
{
    public class CustomLabAlertsQueryHandler : ICustomLabAlertsQueryHandler<CustomLabAlerts>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<CustomLabAlertsQueryHandler> _logger;
        private readonly IStringLocalizer<CustomLabAlertsQueryHandler> _localizer;

        public CustomLabAlertsQueryHandler(
            IUnitOfWork unitOfWork,
            ILogger<CustomLabAlertsQueryHandler> logger,
            IStringLocalizer<CustomLabAlertsQueryHandler> localizer)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _localizer = localizer;
        }

        public async Task<IEnumerable<CustomLabAlerts>> GetAllCustomLabAlerts()
        {
            try
            {
                _logger.LogInformation(_localizer["GettingAllCustomLabAlerts"]);
                var alerts = await _unitOfWork.CustomLabAlertsRepository.GetAllCustomLabAlertsAsync();
                _logger.LogInformation(_localizer["GetAllCustomLabAlertsSuccess"]);
                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetAllCustomLabAlertsError"]);
                throw new Exception(_localizer["GetAllCustomLabAlertsErrorGeneric"], ex);
            }
        }

        public async Task<CustomLabAlerts> GetCustomLabAlertsById(Guid id)
        {
            try
            {
                _logger.LogInformation(_localizer["GettingCustomLabAlertsById"], id);
                var alert = await _unitOfWork.CustomLabAlertsRepository.GetByIdAsync(id);
                if (alert == null)
                {
                    _logger.LogWarning(_localizer["CustomLabAlertsNotFound"], id);
                    throw new KeyNotFoundException(_localizer["CustomLabAlertsNotFound", id]);
                }
                _logger.LogInformation(_localizer["GetCustomLabAlertsByIdSuccess"], id);
                return alert;
            }
            catch (KeyNotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetCustomLabAlertsByIdError"], id);
                throw new Exception(_localizer["GetCustomLabAlertsByIdErrorGeneric", id], ex);
            }
        }

        //public async Task<IEnumerable<CustomLabAlerts>> GetCustomLabAlertsById(Guid Id)
        //{
        //    try
        //    {
        //        _logger.LogInformation(_localizer["GettingCustomLabAlertsByPatientId"], Id);
        //        var alerts = await _unitOfWork.CustomLabAlertsRepository.GetCustomLabAlertsByIdAsync(Id);
        //        _logger.LogInformation(_localizer["GetCustomLabAlertsByPatientIdSuccess"], Id);
        //        return alerts;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, _localizer["GetCustomLabAlertsByPatientIdError"], Id);
        //        throw new Exception(_localizer["GetCustomLabAlertsByPatientIdErrorGeneric", Id], ex);
        //    }
        //}

        public async Task<IEnumerable<CustomLabAlerts>> GetActiveCustomLabAlertsByOrganizationId(Guid organizationId)
        {
            try
            {
                _logger.LogInformation(_localizer["GettingActiveCustomLabAlertsByPatientId"], organizationId);
                var activeAlerts = await _unitOfWork.CustomLabAlertsRepository.GetActiveCustomLabAlertsByOrganizationIdAsync(organizationId);
                _logger.LogInformation(_localizer["GetActiveCustomLabAlertsByPatientIdSuccess"], organizationId);
                return activeAlerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetActiveCustomLabAlertsByPatientIdError"], organizationId);
                throw new Exception(_localizer["GetActiveCustomLabAlertsByPatientIdErrorGeneric", organizationId], ex);
            }
        }
    }
}
