﻿using AlertsContracts;
using AlertsDataAccessLayer.Implementation;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsBusinessLayer.QueryHandler
{
    public class DIAlertsQueryHandler : IDIAlertsQueryHandler<DIAlerts>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DIAlertsQueryHandler> _logger;
        private readonly IStringLocalizer<DIAlertsQueryHandler> _localizer;

        public DIAlertsQueryHandler(
            IUnitOfWork unitOfWork,
            ILogger<DIAlertsQueryHandler> logger,
            IStringLocalizer<DIAlertsQueryHandler> localizer)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _localizer = localizer;
        }

        public async Task<IEnumerable<DIAlerts>> GetAllDIAlerts()
        {
            try
            {
                _logger.LogInformation(_localizer["GettingAllDIAlerts"]);
                var alerts = await _unitOfWork.DIAlertsRepository.GetAllDIAlertsAsync();
                _logger.LogInformation(_localizer["GetAllDIAlertsSuccess"]);
                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetAllDIAlertsError"]);
                throw new Exception(_localizer["GetAllDIAlertsErrorGeneric"], ex);
            }
        }

        public async Task<DIAlerts> GetDIAlertsById(Guid id)
        {
            try
            {
                _logger.LogInformation(_localizer["GettingDIAlertsById"], id);
                var alert = await _unitOfWork.DIAlertsRepository.GetByIdAsync(id);
                if (alert == null)
                {
                    _logger.LogWarning(_localizer["DIAlertsNotFound"], id);
                    throw new KeyNotFoundException(_localizer["DIAlertsNotFound", id]);
                }
                _logger.LogInformation(_localizer["GetDIAlertsByIdSuccess"], id);
                return alert;
            }
            catch (KeyNotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetDIAlertsByIdError"], id);
                throw new Exception(_localizer["GetDIAlertsByIdErrorGeneric", id], ex);
            }
        }

        //public async Task<IEnumerable<DIAlerts>> GetDIAlertsByPatientId(Guid patientId)
        //{
        //    try
        //    {
        //        _logger.LogInformation(_localizer["GettingDIAlertsByPatientId"], patientId);
        //        var alerts = await _unitOfWork.DIAlertsRepository.GetDIAlertsByPatientIdAsync(patientId);
        //        _logger.LogInformation(_localizer["GetDIAlertsByPatientIdSuccess"], patientId);
        //        return alerts;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, _localizer["GetDIAlertsByPatientIdError"], patientId);
        //        throw new Exception(_localizer["GetDIAlertsByPatientIdErrorGeneric", patientId], ex);
        //    }
        //}

        public async Task<IEnumerable<DIAlerts>> GetActiveDIAlertsByOrganizationId(Guid organizationId)
        {
            try
            {
                _logger.LogInformation(_localizer["GettingActiveDIAlertsByPatientId"], organizationId);
                var activeAlerts = await _unitOfWork.DIAlertsRepository.GetActiveDIAlertsByOrganizationIdAsync(organizationId);
                _logger.LogInformation(_localizer["GetActiveDIAlertsByPatientIdSuccess"], organizationId);
                return activeAlerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetActiveDIAlertsByPatientIdError"], organizationId);
                throw new Exception(_localizer["GetActiveDIAlertsByPatientIdErrorGeneric", organizationId], ex);
            }
        }
    }
}