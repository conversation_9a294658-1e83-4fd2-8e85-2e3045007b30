@page "/DataHub"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using System.Text.Json
@inject IDataHubService DataHubService
@inject IStringLocalizer<DataHub> Localizer
@inject ILogger<DataHub> <PERSON>gger
@inject IJSRuntime JSRuntime
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>@Localizer["DataHub"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4">
    <MudPaper Class="pa-4" Elevation="2">
        <MudText Typo="Typo.h4" Class="mb-4">
            <MudIcon Icon="@Icons.Material.Filled.Hub" Class="mr-2" />
            @Localizer["DataHub"]
        </MudText>

        <!-- Query Operations Bar -->
        <MudPaper Class="pa-3 mb-4" Elevation="1">
            <MudGrid AlignItems="Center">
                <MudItem xs="12" md="8">
                    <MudButtonGroup Variant="Variant.Outlined" Color="Color.Primary">
                        <MudButton StartIcon="@Icons.Material.Filled.PlayArrow" 
                                   OnClick="RunNewQuery" 
                                   Disabled="@_isLoading">
                            @Localizer["RunNew"]
                        </MudButton>
                        <MudButton StartIcon="@Icons.Material.Filled.FilterList" 
                                   OnClick="RunSubsetQuery" 
                                   Disabled="@(_isLoading || !_hasResults)">
                            @Localizer["RunSubset"]
                        </MudButton>
                        <MudButton StartIcon="@Icons.Material.Filled.FilterListOff" 
                                   OnClick="RunSubsetNotQuery" 
                                   Disabled="@(_isLoading || !_hasResults)">
                            @Localizer["RunSubsetNot"]
                        </MudButton>
                        <MudButton StartIcon="@Icons.Material.Filled.Save" 
                                   OnClick="SaveQuery" 
                                   Disabled="@(_isLoading || !_hasResults)">
                            @Localizer["SaveQuery"]
                        </MudButton>
                    </MudButtonGroup>
                </MudItem>
                <MudItem xs="12" md="4" Class="text-right">
                    <MudButtonGroup Variant="Variant.Outlined" Color="Color.Secondary">
                        <MudButton StartIcon="@Icons.Material.Filled.Download" 
                                   OnClick="ExportToText" 
                                   Disabled="@(_isLoading || !_hasResults)">
                            @Localizer["ExportText"]
                        </MudButton>
                        <MudButton StartIcon="@Icons.Material.Filled.TableChart" 
                                   OnClick="ExportToCsv" 
                                   Disabled="@(_isLoading || !_hasResults)">
                            @Localizer["ExportCSV"]
                        </MudButton>
                    </MudButtonGroup>
                </MudItem>
            </MudGrid>
        </MudPaper>

        <!-- Registry Tabs -->
        <MudTabs Elevation="1" Rounded="true" Color="Color.Primary" Class="mb-4">
            <MudTabPanel Text="@Localizer["Demographics"]" Icon="@Icons.Material.Filled.Person">
                <DataHubTabContent TabName="Demographics" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Vitals"]" Icon="@Icons.Material.Filled.Favorite">
                <DataHubTabContent TabName="Vitals" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Labs"]" Icon="@Icons.Material.Filled.Science">
                <DataHubTabContent TabName="Labs" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["DiagnosticImaging"]" Icon="@Icons.Material.Filled.MedicalServices">
                <DataHubTabContent TabName="DiagnosticImaging" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Procedures"]" Icon="@Icons.Material.Filled.LocalHospital">
                <DataHubTabContent TabName="Procedures" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["ICD"]" Icon="@Icons.Material.Filled.Code">
                <DataHubTabContent TabName="ICD" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["CPT"]" Icon="@Icons.Material.Filled.Assignment">
                <DataHubTabContent TabName="CPT" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Rx"]" Icon="@Icons.Material.Filled.Medication">
                <DataHubTabContent TabName="Rx" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Immunization"]" Icon="@Icons.Material.Filled.Vaccines">
                <DataHubTabContent TabName="Immunization" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Encounters"]" Icon="@Icons.Material.Filled.EventNote">
                <DataHubTabContent TabName="Encounters" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Allergies"]" Icon="@Icons.Material.Filled.Warning">
                <DataHubTabContent TabName="Allergies" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["MedicalHistory"]" Icon="@Icons.Material.Filled.History">
                <DataHubTabContent TabName="MedicalHistory" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["StructuredData"]" Icon="@Icons.Material.Filled.DataObject">
                <DataHubTabContent TabName="StructuredData" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Referrals"]" Icon="@Icons.Material.Filled.Share">
                <DataHubTabContent TabName="Referrals" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
            <MudTabPanel Text="@Localizer["Reports"]" Icon="@Icons.Material.Filled.Assessment">
                <DataHubTabContent TabName="Reports" 
                                   Filter="@_currentFilter" 
                                   OnFilterChanged="OnFilterChanged" />
            </MudTabPanel>
        </MudTabs>

        <!-- Search and Filter Section -->
        <MudExpansionPanels Elevation="1" Class="mb-4">
            <MudExpansionPanel Text="@Localizer["SearchAndFilters"]" Icon="@Icons.Material.Filled.Search">
                <DataHubFilters Filter="@_currentFilter" 
                                OnFilterChanged="OnFilterChanged" 
                                OnSearch="SearchPatients" />
            </MudExpansionPanel>
        </MudExpansionPanels>

        <!-- Results Section -->
        @if (_isLoading)
        {
            <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mb-4" />
        }

        @if (_hasResults && _patientResults != null)
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudText Typo="Typo.h6" Class="mb-3">
                    @Localizer["SearchResults"] (@_patientResults.Count @Localizer["Patients"])
                </MudText>
                
                <DataHubResults Patients="@_patientResults" 
                                OnPatientSelected="OnPatientSelected" 
                                IsLoading="@_isLoading" />
            </MudPaper>
        }
        else if (!_isLoading && _searchPerformed)
        {
            <MudAlert Severity="Severity.Info" Class="mb-4">
                @Localizer["NoResultsFound"]
            </MudAlert>
        }
    </MudPaper>
</MudContainer>

@if (_isLoading)
{
    <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
    </MudOverlay>
}
