-- Post Deployment Script for TeyaHealth Database
-- Author: <PERSON><PERSON><PERSON>
-- Date: 2025-03-11







BEGIN TRY
    -- Insert Countries if not already present
    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Country])
    BEGIN
        INSERT INTO [AccountService].[Country] (CountryId, CountryName) 
VALUES 
(NEWID(), 'Afghanistan'),
(NEWID(), 'Albania'),
(NEWID(), 'Algeria'),
(NEWID(), 'Andorra'),
(NEWID(), 'Angola'),
(NEWID(), 'Antigua and Barbuda'),
(NEWID(), 'Argentina'),
(NEWID(), 'Armenia'),
(NEWID(), 'Australia'),
(NEWID(), 'Austria'),
(NEWID(), 'Azerbaijan'),
(NEWID(), 'Bahamas'),
(NEWID(), 'Bahrain'),
(NEWID(), 'Bangladesh'),
(NEWID(), 'Barbados'),
(NEWID(), 'Belarus'),
(NEWID(), 'Belgium'),
(NEWID(), 'Belize'),
(NEWID(), 'Benin'),
(NEWID(), 'Bhutan'),
(NEWID(), 'Bolivia'),
(NEWID(), 'Bosnia and Herzegovina'),
(NEWID(), 'Botswana'),
(NEWID(), 'Brazil'),
(NEWID(), 'Brunei'),
(NEWID(), 'Bulgaria'),
(NEWID(), 'Burkina Faso'),
(NEWID(), 'Burundi'),
(NEWID(), 'Cabo Verde'),
(NEWID(), 'Cambodia'),
(NEWID(), 'Cameroon'),
(NEWID(), 'Canada'),
(NEWID(), 'Central African Republic'),
(NEWID(), 'Chad'),
(NEWID(), 'Chile'),
(NEWID(), 'China'),
(NEWID(), 'Colombia'),
(NEWID(), 'Comoros'),
(NEWID(), 'Congo, Democratic Republic of the'),
(NEWID(), 'Congo, Republic of the'),
(NEWID(), 'Costa Rica'),
(NEWID(), 'Cote d''Ivoire'),
(NEWID(), 'Croatia'),
(NEWID(), 'Cuba'),
(NEWID(), 'Cyprus'),
(NEWID(), 'Czech Republic'),
(NEWID(), 'Denmark'),
(NEWID(), 'Djibouti'),
(NEWID(), 'Dominica'),
(NEWID(), 'Dominican Republic'),
(NEWID(), 'Ecuador'),
(NEWID(), 'Egypt'),
(NEWID(), 'El Salvador'),
(NEWID(), 'Equatorial Guinea'),
(NEWID(), 'Eritrea'),
(NEWID(), 'Estonia'),
(NEWID(), 'Eswatini'),
(NEWID(), 'Ethiopia'),
(NEWID(), 'Fiji'),
(NEWID(), 'Finland'),
(NEWID(), 'France'),
(NEWID(), 'Gabon'),
(NEWID(), 'Gambia'),
(NEWID(), 'Georgia'),
(NEWID(), 'Germany'),
(NEWID(), 'Ghana'),
(NEWID(), 'Greece'),
(NEWID(), 'Grenada'),
(NEWID(), 'Guatemala'),
(NEWID(), 'Guinea'),
(NEWID(), 'Guinea-Bissau'),
(NEWID(), 'Guyana'),
(NEWID(), 'Haiti'),
(NEWID(), 'Honduras'),
(NEWID(), 'Hungary'),
(NEWID(), 'Iceland'),
(NEWID(), 'India'),
(NEWID(), 'Indonesia'),
(NEWID(), 'Iran'),
(NEWID(), 'Iraq'),
(NEWID(), 'Ireland'),
(NEWID(), 'Israel'),
(NEWID(), 'Italy'),
(NEWID(), 'Jamaica'),
(NEWID(), 'Japan'),
(NEWID(), 'Jordan'),
(NEWID(), 'Kazakhstan'),
(NEWID(), 'Kenya'),
(NEWID(), 'Kiribati'),
(NEWID(), 'Kuwait'),
(NEWID(), 'Kyrgyzstan'),
(NEWID(), 'Laos'),
(NEWID(), 'Latvia'),
(NEWID(), 'Lebanon'),
(NEWID(), 'Lesotho'),
(NEWID(), 'Liberia'),
(NEWID(), 'Libya'),
(NEWID(), 'Liechtenstein'),
(NEWID(), 'Lithuania'),
(NEWID(), 'Luxembourg'),
(NEWID(), 'Madagascar'),
(NEWID(), 'Malawi'),
(NEWID(), 'Malaysia'),
(NEWID(), 'Maldives'),
(NEWID(), 'Mali'),
(NEWID(), 'Malta'),
(NEWID(), 'Marshall Islands'),
(NEWID(), 'Mauritania'),
(NEWID(), 'Mauritius'),
(NEWID(), 'Mexico'),
(NEWID(), 'Micronesia'),
(NEWID(), 'Moldova'),
(NEWID(), 'Monaco'),
(NEWID(), 'Mongolia'),
(NEWID(), 'Montenegro'),
(NEWID(), 'Morocco'),
(NEWID(), 'Mozambique'),
(NEWID(), 'Myanmar'),
(NEWID(), 'Namibia'),
(NEWID(), 'Nauru'),
(NEWID(), 'Nepal'),
(NEWID(), 'Netherlands'),
(NEWID(), 'New Zealand'),
(NEWID(), 'Nicaragua'),
(NEWID(), 'Niger'),
(NEWID(), 'Nigeria'),
(NEWID(), 'North Korea'),
(NEWID(), 'North Macedonia'),
(NEWID(), 'Norway'),
(NEWID(), 'Oman'),
(NEWID(), 'Pakistan'),
(NEWID(), 'Palau'),
(NEWID(), 'Panama'),
(NEWID(), 'Papua New Guinea'),
(NEWID(), 'Paraguay'),
(NEWID(), 'Peru'),
(NEWID(), 'Philippines'),
(NEWID(), 'Poland'),
(NEWID(), 'Portugal'),
(NEWID(), 'Qatar'),
(NEWID(), 'Romania'),
(NEWID(), 'Russia'),
(NEWID(), 'Rwanda'),
(NEWID(), 'Saint Kitts and Nevis'),
(NEWID(), 'Saint Lucia'),
(NEWID(), 'Saint Vincent and the Grenadines'),
(NEWID(), 'Samoa'),
(NEWID(), 'San Marino'),
(NEWID(), 'Sao Tome and Principe'),
(NEWID(), 'Saudi Arabia'),
(NEWID(), 'Senegal'),
(NEWID(), 'Serbia'),
(NEWID(), 'Seychelles'),
(NEWID(), 'Sierra Leone'),
(NEWID(), 'Singapore'),
(NEWID(), 'Slovakia'),
(NEWID(), 'Slovenia'),
(NEWID(), 'Solomon Islands'),
(NEWID(), 'Somalia'),
(NEWID(), 'South Africa'),
(NEWID(), 'South Korea'),
(NEWID(), 'South Sudan'),
(NEWID(), 'Spain'),
(NEWID(), 'Sri Lanka'),
(NEWID(), 'Sudan'),
(NEWID(), 'Suriname'),
(NEWID(), 'Sweden'),
(NEWID(), 'Switzerland'),
(NEWID(), 'Syria'),
(NEWID(), 'Taiwan'),
(NEWID(), 'Tajikistan'),
(NEWID(), 'Tanzania'),
(NEWID(), 'Thailand'),
(NEWID(), 'Timor-Leste'),
(NEWID(), 'Togo'),
(NEWID(), 'Tonga'),
(NEWID(), 'Trinidad and Tobago'),
(NEWID(), 'Tunisia'),
(NEWID(), 'Turkey'),
(NEWID(), 'Turkmenistan'),
(NEWID(), 'Tuvalu'),
(NEWID(), 'Uganda'),
(NEWID(), 'Ukraine'),
(NEWID(), 'United Arab Emirates'),
(NEWID(), 'United Kingdom'),
(NEWID(), 'United States'),
(NEWID(), 'Uruguay'),
(NEWID(), 'Uzbekistan'),
(NEWID(), 'Vanuatu'),
(NEWID(), 'Vatican City'),
(NEWID(), 'Venezuela'),
(NEWID(), 'Vietnam'),
(NEWID(), 'Yemen'),
(NEWID(), 'Zambia'),
(NEWID(), 'Zimbabwe');

    PRINT 'Countries inserted successfully.';
END
    -- Insert Test Organizations
    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Organization] WHERE OrganizationName = 'Teya Health')
    BEGIN
        INSERT INTO [AccountService].[Organization] 
        ([OrganizationId], [OrganizationName], [Address], [Country], [CreatedDate], [ContactNumber], [IsActive], [Email], [UpdatedDate], [UpdatedBy])
        VALUES (NEWID(), 'Teya Health', '6-th street', 'India', GETDATE(), '**********', 1, '<EMAIL>', GETDATE(), NULL);
    END
	
    PRINT 'Test organizations inserted successfully.';

    DECLARE @Org1 UNIQUEIDENTIFIER;

    -- Fetch Organization IDs based on Organization Names
    SELECT @Org1 = OrganizationId FROM [AccountService].[Organization] WHERE OrganizationName = 'Teya Health';

    -- Insert Roles
    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Role] WHERE RoleName = 'Physician' AND OrganizationId = @Org1)
    BEGIN
        INSERT INTO [AccountService].[Role] 
        ([RoleId], [RoleName], [CreatedDate], [UpdatedDate], [UpdatedBy], [OrganizationId], [IsActive])
        VALUES (NEWID(), 'Physician', GETDATE(), NULL, NULL, @Org1, 1);
    END

    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Role] WHERE RoleName = 'Receptionist' AND OrganizationId = @Org1)
    BEGIN
        INSERT INTO [AccountService].[Role] 
        ([RoleId], [RoleName], [CreatedDate], [UpdatedDate], [UpdatedBy], [OrganizationId], [IsActive])
        VALUES (NEWID(), 'Receptionist', GETDATE(), NULL, NULL, @Org1, 1);
    END

    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Role] WHERE RoleName = 'Guest' AND OrganizationId = @Org1)
    BEGIN
        INSERT INTO [AccountService].[Role] 
        ([RoleId], [RoleName], [CreatedDate], [UpdatedDate], [UpdatedBy], [OrganizationId], [IsActive])
        VALUES (NEWID(), 'Guest', GETDATE(), NULL, NULL, @Org1, 1);
    END

    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Role] WHERE RoleName = 'System operation user' AND OrganizationId = @Org1)
    BEGIN
        INSERT INTO [AccountService].[Role] 
        ([RoleId], [RoleName], [CreatedDate], [UpdatedDate], [UpdatedBy], [OrganizationId], [IsActive])
        VALUES (NEWID(), 'System operation user', GETDATE(), NULL, NULL, @Org1, 1);
    END

    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Role] WHERE RoleName = 'Admin' AND OrganizationId = @Org1)
    BEGIN
        INSERT INTO [AccountService].[Role] 
        ([RoleId], [RoleName], [CreatedDate], [UpdatedDate], [UpdatedBy], [OrganizationId], [IsActive])
        VALUES (NEWID(), 'Admin', GETDATE(), NULL, NULL, @Org1, 1);
    END

    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Role] WHERE RoleName = 'Patient' AND OrganizationId = @Org1)
    BEGIN
        INSERT INTO [AccountService].[Role] 
        ([RoleId], [RoleName], [CreatedDate], [UpdatedDate], [UpdatedBy], [OrganizationId], [IsActive])
        VALUES (NEWID(), 'Patient', GETDATE(), NULL, NULL, @Org1, 1);
    END

    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Role] WHERE RoleName = 'Provider' AND OrganizationId = @Org1)
    BEGIN
        INSERT INTO [AccountService].[Role] 
        ([RoleId], [RoleName], [CreatedDate], [UpdatedDate], [UpdatedBy], [OrganizationId], [IsActive])
        VALUES (NEWID(), 'Provider', GETDATE(), NULL, NULL, @Org1, 1);
    END

    -- Insert Facilities
    IF NOT EXISTS (SELECT 1 FROM [AccountService].[Facility] WHERE FacilityName = 'Clinic' AND OrganizationId = @Org1)
    BEGIN
        INSERT INTO [AccountService].[Facility] 
        ([FacilityId], [FacilityName], [StreetName], [City], [State], [Zipcode], [Country], [CreatedDate], [UpdatedDate], [OrganizationId], [IsActive], [UpdatedBy])
        VALUES (NEWID(), 'Clinic', '6th Street', 'Noida', 'Delhi', '110098', 'India', GETDATE(), NULL, @Org1, 1, NULL);
    END
	
    PRINT 'Post-deployment script executed successfully: Roles and Facilities inserted.';

    -- Insert PagePaths
DECLARE @CreatedBy UNIQUEIDENTIFIER;
SET @CreatedBy = '99BA6D72-9179-4852-B22F-56C834DE46BA';

INSERT INTO [AccountService].[PagePath] 
([PageId], [PagePathValue], [CreatedBy], [UpdatedBy], [CreatedDate], [UpdatedDate], [IsActive]) 
SELECT 
    NEWID(), PagePathValue, @CreatedBy, NULL, GETDATE(), NULL, 1
FROM 
    (VALUES
    ('/AmbientSolution'),
    ('/Menu'),
    ('/Chart'),
    ('/Appointments'),
    ('/ManageProfile'),
    ('/Message'),
    ('/Practice'),
    ('/Document'),
    ('/Providers'),
    ('/ProductFeatureSettings'),
    ('/Patients'),
    ('/Staff'),
    ('/LicenseActivation'),
    ('/License'),
    ('/Security'),
    ('/UserManagement'),
    ('/Templates'),
    ('/Config'),
	('/ClaimsLookup'),
	('/Encounters'),
    ('/about')) AS Paths(PagePathValue)
WHERE 
    NOT EXISTS (
        SELECT 1 
        FROM [AccountService].[PagePath] 
        WHERE PagePathValue = Paths.PagePathValue
    );

-- Insert PageRoleMappings
DECLARE @AdminRoleId UNIQUEIDENTIFIER, @OrganizationId UNIQUEIDENTIFIER;

SELECT @AdminRoleId = RoleId, @OrganizationId = OrganizationId
FROM [AccountService].[Role]
WHERE RoleName = 'Admin';

INSERT INTO [AccountService].[PageRoleMappings] (
    [Id], [PagePath], [RoleId], [RoleName], [OrganizationId], [CreatedBy], 
    [UpdatedBy], [CreatedDate], [UpdatedDate], [IsActive], [HasAccess], [IsModified]
) 
SELECT 
    NEWID(), PagePath, @AdminRoleId, 'Admin', @OrganizationId, @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0
FROM 
    (VALUES
    ('/Providers'),
    ('/Templates'),
    ('/Document'),
    ('/Patients'),
    ('/ProductFeatureSettings'),
    ('/about'),
    ('/Appointments'),
    ('/UserManagement'),
    ('/License'),
    ('/Chart'),
    ('/Security'),
    ('/Practice'),
    ('/Config'),
    ('/LicenseActivation'),
    ('/Message'),
    ('/ManageProfile'),
    ('/Staff'),
    ('/AmbientSolution'),
    ('/ClaimsLookup'),
	('/Encounters'),
    ('/Menu')) AS Mappings(PagePath)
WHERE 
    NOT EXISTS (
        SELECT 1 
        FROM [AccountService].[PageRoleMappings] 
        WHERE PagePath = Mappings.PagePath AND RoleId = @AdminRoleId
    );

DECLARE @ProviderRoleId UNIQUEIDENTIFIER, 
        @PatientRoleId UNIQUEIDENTIFIER, 
        @FacilityRoleId UNIQUEIDENTIFIER, 
        @ReceptionistRoleId UNIQUEIDENTIFIER, 
        @AccountantRoleId UNIQUEIDENTIFIER;

-- Get Role IDs for each role
SELECT @ProviderRoleId = RoleId FROM [AccountService].[Role] WHERE RoleName = 'Provider';
SELECT @PatientRoleId = RoleId FROM [AccountService].[Role] WHERE RoleName = 'Patient';
SELECT @FacilityRoleId = RoleId FROM [AccountService].[Role] WHERE RoleName = 'Facility';
SELECT @ReceptionistRoleId = RoleId FROM [AccountService].[Role] WHERE RoleName = 'Receptionist';
SELECT @AccountantRoleId = RoleId FROM [AccountService].[Role] WHERE RoleName = 'Accountant';

-- Insert data only if it doesn't already exist
IF NOT EXISTS (SELECT 1 FROM [AccountService].[PreDefinedPageRoleMappings] WHERE [PagePath] = '/Appointments' AND [RoleId] = @ProviderRoleId)
BEGIN
    INSERT INTO [AccountService].[PreDefinedPageRoleMappings] 
    ([Id], [PagePath], [RoleId], [RoleName], [CreatedBy], [UpdatedBy], [CreatedDate], [UpdatedDate], [IsActive], [HasAccess], [IsModified]) 
    VALUES
    -- Provider Role
    (NEWID(), '/Appointments', @ProviderRoleId, 'Provider', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Chart', @ProviderRoleId, 'Provider', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/ManageProfile', @ProviderRoleId, 'Provider', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Message', @ProviderRoleId, 'Provider', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Patients', @ProviderRoleId, 'Provider', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Document', @ProviderRoleId, 'Provider', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),

    -- Patient Role
    (NEWID(), '/Appointments', @PatientRoleId, 'Patient', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Chart', @PatientRoleId, 'Patient', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/ManageProfile', @PatientRoleId, 'Patient', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Message', @PatientRoleId, 'Patient', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/about', @PatientRoleId, 'Patient', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),

    -- Facility Role
    (NEWID(), '/Appointments', @FacilityRoleId, 'Facility', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Practice', @FacilityRoleId, 'Facility', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Providers', @FacilityRoleId, 'Facility', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Patients', @FacilityRoleId, 'Facility', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Document', @FacilityRoleId, 'Facility', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Config', @FacilityRoleId, 'Facility', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),

    -- Receptionist Role
    (NEWID(), '/Appointments', @ReceptionistRoleId, 'Receptionist', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/ManageProfile', @ReceptionistRoleId, 'Receptionist', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Message', @ReceptionistRoleId, 'Receptionist', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Patients', @ReceptionistRoleId, 'Receptionist', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Staff', @ReceptionistRoleId, 'Receptionist', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),

    -- Accountant Role
    (NEWID(), '/License', @AccountantRoleId, 'Accountant', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/LicenseActivation', @AccountantRoleId, 'Accountant', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Templates', @AccountantRoleId, 'Accountant', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0),
    (NEWID(), '/Config', @AccountantRoleId, 'Accountant', @CreatedBy, NULL, GETDATE(), NULL, 1, 1, 0);
END;

-- Insert PlanTypes
INSERT INTO [AccountService].[PlanTypes] ([Id], [PlanName], [CreatedDate], [UpdatedDate])
SELECT 
    NEWID(), PlanName, GETDATE(), NULL
FROM 
    (VALUES
    ('Enterprise'),
    ('Pro'),
    ('Free'),
    ('Team')) AS Plans(PlanName)
WHERE 
    NOT EXISTS (
        SELECT 1 
        FROM [AccountService].[PlanTypes] 
        WHERE PlanName = Plans.PlanName
    );

  --Predefined templates
IF NOT EXISTS (SELECT 1 FROM [EncounterNotesService].[ProgressNotesPredefinedTemplates])
BEGIN
	INSERT INTO [EncounterNotesService].[ProgressNotesPredefinedTemplates] 
	(Id, Template, CreatedDate, TemplateName, IsDefault, VisitType)
	VALUES
	(NEWID(), '{"Subjective":{"Chief Complaint":{"Instructions":"","SectionStyle":"Auto"},"Current Medication":{"Instructions":"","SectionStyle":"Auto"},"HPI":{"Instructions":"","SectionStyle":"Auto"},"Allergies":{"Instructions":"","SectionStyle":"Auto"},"Surgical History":{"Instructions":"","SectionStyle":"Auto"},"Hospitalization":{"Instructions":"","SectionStyle":"Auto"},"Family History":{"Instructions":"","SectionStyle":"Auto"},"Social History":{"Instructions":"","SectionStyle":"Auto"},"Gyn History":{"Instructions":"","SectionStyle":"Auto"},"Obstetric history":{"Instructions":"","SectionStyle":"Auto"},"Review of System":{"Instructions":"","SectionStyle":"Auto"}},"Objective":{"Vitals":{"Instructions":"","SectionStyle":"Auto"},"Past Results":{"Instructions":"","SectionStyle":"Auto"},"Physical Examination":{"Instructions":"","SectionStyle":"Auto"}},"Assessment":{"Assessment":{"Instructions":"","SectionStyle":"Auto"}},"Plan":{"Treatment":{"Instructions":"","SectionStyle":"Auto"},"Procedures":{"Instructions":"","SectionStyle":"Auto"},"Immunization":{"Instructions":"","SectionStyle":"Auto"},"Diagnostic Imaging":{"Instructions":"","SectionStyle":"Auto"},"Lab Results":{"Instructions":"","SectionStyle":"Auto"},"Preventive Medicine":{"Instructions":"","SectionStyle":"Auto"},"Billing":{"Instructions":"","SectionStyle":"Auto"}}}', GETDATE(), 'Default', 1, 'Default'),
	(NEWID(), '{"Subjective":{"Chief Complaint":{"Instructions":"","SectionStyle":"Auto"},"Current Medication":{"Instructions":"","SectionStyle":"Auto"},"HPI":{"Instructions":"","SectionStyle":"Auto"},"Allergies":{"Instructions":"","SectionStyle":"Auto"},"Surgical History":{"Instructions":"","SectionStyle":"Auto"},"Hospitalization":{"Instructions":"","SectionStyle":"Auto"},"Family History":{"Instructions":"","SectionStyle":"Auto"},"Social History":{"Instructions":"","SectionStyle":"Auto"},"Gyn History":{"Instructions":"","SectionStyle":"Auto"},"Obstetric history":{"Instructions":"","SectionStyle":"Auto"},"Review of System":{"Instructions":"","SectionStyle":"Auto"}},"Objective":{"Vitals":{"Instructions":"","SectionStyle":"Auto"},"Past Results":{"Instructions":"","SectionStyle":"Auto"},"Physical Examination":{"Instructions":"","SectionStyle":"Auto"}},"Assessment":{"Assessment":{"Instructions":"","SectionStyle":"Auto"}},"Plan":{"Treatment":{"Instructions":"","SectionStyle":"Auto"},"Procedures":{"Instructions":"","SectionStyle":"Auto"},"Immunization":{"Instructions":"","SectionStyle":"Auto"},"Diagnostic Imaging":{"Instructions":"","SectionStyle":"Auto"},"Lab Results":{"Instructions":"","SectionStyle":"Auto"},"Preventive Medicine":{"Instructions":"","SectionStyle":"Auto"},"Billing":{"Instructions":"","SectionStyle":"Auto"}}}', GETDATE(), 'Default Intervention', 1, 'Intervention Visit'),
	(NEWID(), '{"Subjective":{"Chief Complaint":{"Instructions":"","SectionStyle":"Auto"},"Current Medication":{"Instructions":"","SectionStyle":"Auto"},"HPI":{"Instructions":"","SectionStyle":"Auto"},"Allergies":{"Instructions":"","SectionStyle":"Auto"},"Surgical History":{"Instructions":"","SectionStyle":"Auto"},"Hospitalization":{"Instructions":"","SectionStyle":"Auto"},"Family History":{"Instructions":"","SectionStyle":"Auto"},"Social History":{"Instructions":"","SectionStyle":"Auto"},"Gyn History":{"Instructions":"","SectionStyle":"Auto"},"Obstetric history":{"Instructions":"","SectionStyle":"Auto"},"Review of System":{"Instructions":"","SectionStyle":"Auto"}},"Objective":{"Vitals":{"Instructions":"","SectionStyle":"Auto"},"Past Results":{"Instructions":"","SectionStyle":"Auto"},"Vision Examination":{"Instructions":"","SectionStyle":"Auto"}},"Assessment":{"Assessment":{"Instructions":"","SectionStyle":"Auto"}},"Plan":{"Treatment":{"Instructions":"","SectionStyle":"Auto"},"Procedures":{"Instructions":"","SectionStyle":"Auto"},"Immunization":{"Instructions":"","SectionStyle":"Auto"},"Diagnostic Imaging":{"Instructions":"","SectionStyle":"Auto"},"Lab Results":{"Instructions":"","SectionStyle":"Auto"},"Preventive Medicine":{"Instructions":"","SectionStyle":"Auto"},"Billing":{"Instructions":"","SectionStyle":"Auto"}}}', GETDATE(), 'Default Vision', 1, 'Vision Visit'),
	(NEWID(), '{"Subjective":{"Chief Complaint":{"Instructions":"","SectionStyle":"Auto"},"Current Medication":{"Instructions":"","SectionStyle":"Auto"},"HPI":{"Instructions":"","SectionStyle":"Auto"},"Allergies":{"Instructions":"","SectionStyle":"Auto"},"Surgical History":{"Instructions":"","SectionStyle":"Auto"},"Hospitalization":{"Instructions":"","SectionStyle":"Auto"},"Family History":{"Instructions":"","SectionStyle":"Auto"},"Social History":{"Instructions":"","SectionStyle":"Auto"},"Gyn History":{"Instructions":"","SectionStyle":"Auto"},"Obstetric history":{"Instructions":"","SectionStyle":"Auto"},"Review of System":{"Instructions":"","SectionStyle":"Auto"}},"Objective":{"Vitals":{"Instructions":"","SectionStyle":"Auto"},"Past Results":{"Instructions":"","SectionStyle":"Auto"},"Physical Examination":{"Instructions":"","SectionStyle":"Auto"}},"Assessment":{"Assessment":{"Instructions":"","SectionStyle":"Auto"}},"Plan":{"Treatment":{"Instructions":"","SectionStyle":"Auto"},"Procedures":{"Instructions":"","SectionStyle":"Auto"},"Immunization":{"Instructions":"","SectionStyle":"Auto"},"Diagnostic Imaging":{"Instructions":"","SectionStyle":"Auto"},"Lab Results":{"Instructions":"","SectionStyle":"Auto"},"Preventive Medicine":{"Instructions":"","SectionStyle":"Auto"},"Billing":{"Instructions":"","SectionStyle":"Auto"}},"Therapeutic Interventions":{"Therapeutic Interventions":{"Instructions":"","SectionStyle":"Auto"}}}', GETDATE(), 'Default Physical Therapy', 1, 'Physical Visit'),
	(NEWID(), '{"Subjective":{"Chief Complaint":{"Instructions":"","SectionStyle":"Auto"},"Allergies":{"Instructions":"","SectionStyle":"Auto"}},"Objective":{"Vitals":{"Instructions":"","SectionStyle":"Auto"},"Physical Examination":{"Instructions":"","SectionStyle":"Auto"}},"Assessment":{"Assessment":{"Instructions":"","SectionStyle":"Auto"}},"Plan":{"Procedures":{"Instructions":"","SectionStyle":"Auto"},"Billing":{"Instructions":"","SectionStyle":"Auto"}}}', GETDATE(), 'Default Operative Report', 1, 'Operative Report Visit'),
	(NEWID(), '{"Subjective":{"Chief Complaint":{"Instructions":"","SectionStyle":"Auto"},"Current Medication":{"Instructions":"","SectionStyle":"Auto"},"HPI":{"Instructions":"","SectionStyle":"Auto"},"Allergies":{"Instructions":"","SectionStyle":"Auto"},"Review of System":{"Instructions":"","SectionStyle":"Auto"}},"Objective":{"Vitals":{"Instructions":"","SectionStyle":"Auto"},"Physical Examination":{"Instructions":"","SectionStyle":"Auto"}},"Assessment":{"Assessment":{"Instructions":"","SectionStyle":"Auto"}},"Plan":{"Treatment":{"Instructions":"","SectionStyle":"Auto"},"Billing":{"Instructions":"","SectionStyle":"Auto"}}}', GETDATE(), 'Default Regular Visit', 1, 'Regular Visit');
END;

    --FDB Medication Route Look Up
IF NOT EXISTS (SELECT 1 FROM [FDB].[MedicationRouteLookUp])
BEGIN
    INSERT INTO [FDB].[MedicationRouteLookUp] ([MED_ROUTE_ID], [Route_Name])
    VALUES 
    ('00001', 'Buccal'),
    ('00002', 'Combination'),
    ('00003', 'Dental'),
    ('00004', 'Epidural'),
    ('00005', 'Topical'),
    ('00006', 'Intra-arterial'),
    ('00007', 'Intracavernosal'),
    ('00008', 'Intradermal'),
    ('00009', 'Injection'),
    ('00010', 'Implant'),
    ('00011', 'Intramuscular'),
    ('00012', 'Inhalation'),
    ('00013', 'Intraocular'),
    ('00014', 'Intraperitoneal'),
    ('00015', 'Irrigation'),
    ('00016', 'Intravesical'),
    ('00017', 'Intrathecal'),
    ('00018', 'Intrauterine'),
    ('00019', 'Intravenous'),
    ('00020', 'Intra-articular'),
    ('00021', 'Mucous Membrane'),
    ('00022', 'Nasal'),
    ('00023', 'Ophthalmic'),
    ('00024', 'Oral'),
    ('00025', 'Otic'),
    ('00026', 'Perfusion'),
    ('00027', 'Rectal'),
    ('00028', 'Subcutaneous'),
    ('00029', 'Sublingual'),
    ('00030', 'Transdermal'),
    ('00031', 'Translingual'),
    ('00032', 'Urethral'),
    ('00033', 'Vaginal'),
    ('00034', 'In Vitro'),
    ('00035', 'Miscell. (Med.Supl.;Non-Drugs)'),
    ('00036', 'Intrapleural');
END;

 -- SOAP notes components
 IF NOT EXISTS (SELECT 1 FROM [EncounterNotesService].[SoapNotesComponents])
BEGIN
    INSERT INTO [EncounterNotesService].[SoapNotesComponents] ([Id], [Name])
    VALUES 
        (NEWID(), 'Chief Complaint'),
        (NEWID(), 'Current Medication'),
        (NEWID(), 'Immunization'),
        (NEWID(), 'Procedure'),
        (NEWID(), 'Allergies'),
        (NEWID(), 'Assessments'),
        (NEWID(), 'Diagnostic Imaging'),
        (NEWID(), 'Examinations'),
        (NEWID(), 'Family History'),
        (NEWID(), 'Gynecology History'),
        (NEWID(), 'Hospitalization'),
        (NEWID(), 'HPI'),
        (NEWID(), 'Medical History'),
        (NEWID(), 'Obstetric History'),
        (NEWID(), 'Past Results'),
        (NEWID(), 'Physical Examination'),
        (NEWID(), 'Physical Therapy'),
        (NEWID(), 'Referral Outgoing'),
        (NEWID(), 'Social History'),
        (NEWID(), 'Subjective Review Of System'),
        (NEWID(), 'Surgical History'),
        (NEWID(), 'Therapeutic Interventions'),
        (NEWID(), 'Vitals');
END;

  --Therapeutic Interventions list
IF NOT EXISTS (SELECT 1 FROM [EncounterNotesService].[TherapeuticInterventionsList])
BEGIN
	INSERT INTO [EncounterNotesService].[TherapeuticInterventionsList]
	([ID], [TherapyType], [Description])
	VALUES
	    (NEWID(), 'Cognitive-Behavioral Therapy (CBT)', 'Identifying and challenging negative thought patterns to modify behavior and improve emotional well-being.'),
	    (NEWID(), 'Mindfulness Meditation', 'Cultivating present-moment awareness to reduce stress, enhance focus, and promote overall mental well-being.'),
	    (NEWID(), 'Dialectical Behavior Therapy (DBT)', 'Balancing acceptance and change, particularly useful for emotional regulation and interpersonal effectiveness.'),
	    (NEWID(), 'Art Therapy', 'Utilizing creative expression to explore emotions, reduce stress, and promote self-discovery.'),
	    (NEWID(), 'Play Therapy', 'Supporting children in expressing and processing emotions through play-based activities.'),
	    (NEWID(), 'Psychodynamic Therapy', 'Exploring unconscious processes and early life experiences to gain insight into current behavior and emotions.'),
	    (NEWID(), 'Narrative Therapy', 'Restructuring and reauthoring personal narratives to promote positive change.'),
	    (NEWID(), 'Eye Movement Desensitization and Reprocessing (EMDR)', 'Relieving symptoms of trauma through guided eye movements.'),
	    (NEWID(), 'Solution-Focused Brief Therapy (SFBT)', 'Focusing on solutions and goals rather than problems, promoting a forward-oriented approach.'),
	    (NEWID(), 'Expressive Writing Therapy', 'Encouraging individuals to write about their thoughts and emotions to foster emotional processing.'),
	    (NEWID(), 'Animal-Assisted Therapy', 'Involving animals to provide emotional support and facilitate therapeutic outcomes.'),
	    (NEWID(), 'Music Therapy', 'Using musical activities to enhance emotional expression, communication, and stress reduction.'),
	    (NEWID(), 'Biofeedback', 'Monitoring physiological functions to gain awareness and control over bodily responses, particularly helpful for stress-related conditions.'),
	    (NEWID(), 'Interpersonal Therapy (IPT)', 'Addressing interpersonal issues to improve relationships and overall mental health.'),
	    (NEWID(), 'Reality Therapy', 'Focusing on the present and developing responsible, effective choices to meet basic needs.'),
	    (NEWID(), 'Humor Therapy', 'Incorporating humor and laughter to reduce stress and improve mood.'),
	    (NEWID(), 'Gestalt Therapy', 'Focusing on the present moment and personal responsibility for one''s own actions and choices.'),
	    (NEWID(), 'Occupational Therapy', 'Utilizing purposeful activities to improve daily functioning and promote mental health.'),
	    (NEWID(), 'Adventure Therapy', 'Engaging in outdoor activities to promote personal growth, teamwork, and resilience.'),
	    (NEWID(), 'Emotion-Focused Therapy (EFT)', 'Helping individuals understand and regulate their emotions for improved mental well-being.');
END;

   ----- Vaccines Data List (CPT Descritpion, CPT Code, CVX Code, Vaccine Name)
IF NOT EXISTS (SELECT 1 FROM [EncounterNotesService].[Vaccines])
BEGIN
INSERT INTO [EncounterNotesService].[Vaccines]
    ([Id]
    ,[CPTDescription]
    ,[CPTCode]
    ,[CVXCode]
	,[VaccineName])
VALUES
    (NEWID(), 'Immune globulin (Ig), human, for intramuscular use', '90281', '86', 'IG'),
    (NEWID(), 'Immune globulin (IgIV), human, for intravenous use', '90283', '87', 'IGIV'),
    (NEWID(), 'Botulinum antitoxin, equine, any route', '90287', '27', 'botulinum antitoxin'),
    (NEWID(), 'Cytomegalovirus immune globulin (CMV-IgIV), human, for intravenous use', '90291', '29', 'CMVIG'),
    (NEWID(), 'Diphtheria antitoxin, equine, any route', '90296', '12', 'diphtheria antitoxin'),
    (NEWID(), 'Hepatitis B immune globulin (HBIg), human, for intramuscular use', '90371', '30', 'HBIG'),
    (NEWID(), 'Rabies immune globulin (RIg), human, for intramuscular and/or subcutaneous use', '90375', '34', 'RIG'),
    (NEWID(), 'Rabies immune globulin, heat-treated (RIg-HT), human, for intramuscular and/or subcutaneous use', '90376', '34', 'RIG'),
    (NEWID(), 'Respiratory syncytial virus, monoclonal antibody, recombinant, for intramuscular use, 50 mg, each', '90378', '93', 'RSV-MAb'),
    (NEWID(), 'Respiratory syncytial virus, monoclonal antibody, seasonal dose; 0.5 mL dosage, for intramuscular use', '90380', '306', 'RSV, mAb, nirsevimab-alip, 0.5 mL, neonate to 24 months'),
    (NEWID(), 'Respiratory syncytial virus, monoclonal antibody, seasonal dose; 1 mL dosage, for intramuscular use', '90381', '307', 'RSV, mAb, nirsevimab-alip, 1 mL, neonate to 24 months'),
    (NEWID(), 'Tetanus immune globulin (TIg), human, for intramuscular use', '90389', '13', 'TIG'),
    (NEWID(), 'Vaccinia immune globulin, human, for intramuscular use', '90393', '79', 'vaccinia immune globulin'),
    (NEWID(), 'Varicella-zoster immune globulin, human, for intramuscular use', '90396', '36', 'VZIG'),
    (NEWID(), 'H1N1 immunization administration (intramuscular, intranasal), including counseling when performed', '90470', '128', 'Novel Influenza-H1N1-09, all formulations'),
    (NEWID(), 'Adenovirus vaccine, type 4, live, for oral use', '90476', '54', 'adenovirus, type 4'),
    (NEWID(), 'Adenovirus vaccine, type 7, live, for oral use', '90477', '55', 'adenovirus, type 7'),
    (NEWID(), 'Anthrax vaccine, for subcutaneous or intramuscular use', '90581', '24', 'Anthrax, pre-exposure prophylaxis, post-exposure prophylaxis'),
    (NEWID(), 'Anthrax vaccine, for subcutaneous or intramuscular use', '90581', '318', 'Anthrax, post-exposure prophylaxis'),
    (NEWID(), 'Bacillus Calmette-Guerin vaccine (BCG) for tuberculosis, live, for percutaneous use', '90585', '19', 'BCG'),
    (NEWID(), 'Dengue vaccine, quadrivalent, live, 3 dose schedule, for subcutaneous use', '90587', '56', 'dengue fever tetravalent'),
    (NEWID(), 'Chikungunya virus vaccine, live attenuated, for intramuscular use', '90589', '317', 'Chikungunya live attenuated vaccine, 0.5 mL, PF'),
    (NEWID(), 'Smallpox and monkeypox vaccine, attenuated vaccinia virus, live, non-replicating, preservative free, 0.5 mL dosage, suspension, for subcutaneous use', '90611', '206', 'Vaccinia, smallpox Mpox vaccine live, PF, SQ or ID injection'),
    (NEWID(), 'Meningococcal conjugate vaccine, serogroups A, C, W, Y, quadrivalent, tetanus toxoid carrier (MenACWY-TT), for intramuscular use', '90619', '203', 'meningococcal conjugate quadrivalent, MenACWY-TT (MCV4)'),
    (NEWID(), 'Meningococcal recombinant protein and outer membrane vesicle vaccine, serogroup B (MenB-4C), 2 dose schedule, for intramuscular use', '90620', '163', 'meningococcal B, OMV'),
    (NEWID(), 'Meningococcal recombinant lipoprotein vaccine, serogroup B (MenB-FHbp), 2 or 3 dose schedule, for intramuscular use', '90621', '162', 'meningococcal B, recombinant'),
    (NEWID(), 'Vaccinia (smallpox) virus vaccine, live, lyophilized, 0.3 mL dosage, for percutaneous use', '90622', '75', 'vaccinia (smallpox)'),
    (NEWID(), 'Meningococcal pentavalent vaccine, conjugated Men A, C, W, Y- tetanus toxoid carrier, and Men B-FHbp, for intramuscular use', '90623', '316', 'Meningococcal polysaccharide (MenACWY-TT conjugate), (MenB), PF'),
    (NEWID(), 'Meningococcal pentavalent vaccine, Men B-4C recombinant proteins and outer membrane vesicle and conjugated Men A, C, W, Y-diphtheria toxoid carrier, for intramuscular use', '90624', '328', 'Meningococcal oligosaccharide (MenACWY), (MenB), PF'),
    (NEWID(), 'Cholera vaccine, live, adult dosage, 1 dose schedule, for oral use', '90625', '174', 'cholera, live attenuated'),
    (NEWID(), 'Tick-borne encephalitis virus vaccine, inactivated; 0.25 mL dosage, for intramuscular use', '90626', '223', 'Tick-borne encephalitis, inactivated, PF, 0.25mL'),
    (NEWID(), 'Tick-borne encephalitis virus vaccine, inactivated; 0.5 mL dosage, for intramuscular use', '90627', '224', 'Tick-borne encephalitis, inactivated, PF, 0.5mL'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (IIV4), split virus, preservative free, for intradermal use', '90630', '166', 'influenza, intradermal, quadrivalent, preservative free'),
    (NEWID(), 'Hepatitis A vaccine (HepA), adult dosage, for intramuscular use', '90632', '52', 'Hep A, adult'),
    (NEWID(), 'Hepatitis A vaccine (HepA), pediatric/adolescent dosage-2 dose schedule, for intramuscular use', '90633', '83', 'Hep A, ped/adol, 2 dose'),
    (NEWID(), 'Hepatitis A vaccine (HepA), pediatric/adolescent dosage-3 dose schedule, for intramuscular use', '90634', '84', 'Hep A, ped/adol, 3 dose'),
    (NEWID(), 'Hepatitis A and hepatitis B vaccine (HepA-HepB), adult dosage, for intramuscular use', '90636', '104', 'Hep A-Hep B'),
    (NEWID(), 'Meningococcal conjugate vaccine, serogroups C & Y and Haemophilus influenzae type b vaccine (Hib-MenCY), 4 dose schedule, when administered to children 6 weeks-18 months of age, for intramuscular use', '90644', '148', 'Meningococcal C/Y-HIB PRP'),
    (NEWID(), 'Hemophilus influenza b vaccine (Hib), HbOC conjugate (4 dose schedule), for intramuscular use', '90645', '47', 'Hib (HbOC)'),
    (NEWID(), 'Hemophilus influenza b vaccine (Hib), PRP-D conjugate, for booster use only, intramuscular use', '90646', '46', 'Hib (PRP-D)'),
    (NEWID(), 'Haemophilus influenzae type b vaccine (Hib), PRP-OMP conjugate, 3 dose schedule, for intramuscular use', '90647', '49', 'Hib (PRP-OMP)'),
    (NEWID(), 'Haemophilus influenzae type b vaccine (Hib), PRP-T conjugate, 4 dose schedule, for intramuscular use', '90648', '48', 'Hib (PRP-T)'),
    (NEWID(), 'Human Papillomavirus vaccine, types 6, 11, 16, 18, quadrivalent (4vHPV), 3 dose schedule, for intramuscular use', '90649', '62', 'HPV, quadrivalent'),
    (NEWID(), 'Human Papillomavirus vaccine, types 16, 18, bivalent (2vHPV), 3 dose schedule, for intramuscular use', '90650', '118', 'HPV, bivalent'),
    (NEWID(), 'Human Papillomavirus vaccine types 6, 11, 16, 18, 31, 33, 45, 52, 58, nonavalent (9vHPV), 2 or 3 dose schedule, for intramuscular use', '90651', '165', 'HPV9'),
    (NEWID(), 'Influenza vaccine, inactivated (IIV), subunit, adjuvanted, for intramuscular use', '90653', '168', 'Influenza, adjuvanted, trivalent, PF'),
    (NEWID(), 'Influenza virus vaccine, trivalent (IIV3), split virus, preservative-free, for intradermal use', '90654', '144', 'influenza, seasonal, intradermal, preservative free'),
    (NEWID(), 'Influenza virus vaccine, trivalent (IIV3), split virus, preservative free, 0.5 mL dosage, for intramuscular use', '90656', '140', 'Influenza, split virus, trivalent, PF'),
    (NEWID(), 'Influenza virus vaccine, trivalent (IIV3), split virus, 0.25 mL dosage, for intramuscular use', '90657', '141', 'Influenza, split virus, trivalent, preservative'),
    (NEWID(), 'Influenza virus vaccine, trivalent (IIV3), split virus, 0.5 mL dosage, for intramuscular use', '90658', '141', 'Influenza, split virus, trivalent, preservative'),
    (NEWID(), 'Influenza virus vaccine, whole virus, for intramuscular or jet injection use', '90659', '16', 'influenza, whole'),
    (NEWID(), 'Influenza virus vaccine, trivalent, live (LAIV3), for intranasal use', '90660', '111', 'Influenza, live, trivalent, intranasal'),
    (NEWID(), 'Influenza virus vaccine, trivalent (ccIIV3), derived from cell cultures, subunit, antibiotic free, 0.5 mL dosage, for intramuscular use', '90661', '320', 'Influenza, MDCK, trivalent, preservative'),
    (NEWID(), 'Influenza virus vaccine, trivalent (ccIIV3), derived from cell cultures, subunit, antibiotic free, 0.5 mL dosage, for intramuscular use', '90661', '153', 'Influenza, MDCK, trivalent, PF'),
    (NEWID(), 'Influenza virus vaccine (IIV), split virus, preservative free, enhanced immunogenicity via increased antigen content, for intramuscular use', '90662', '197', 'Influenza, high-dose, quadrivalent, PF'),
    (NEWID(), 'Influenza virus vaccine (IIV), split virus, preservative free, enhanced immunogenicity via increased antigen content, for intramuscular use', '90662', '135', 'Influenza, high-dose, trivalent, PF'),
    (NEWID(), 'Influenza virus vaccine, pandemic formulation, H1N1', '90663', '128', 'Novel Influenza-H1N1-09, all formulations'),
    (NEWID(), 'Influenza virus vaccine, live (LAIV), pandemic formulation, for intranasal use', '90664', '125', 'Novel Influenza-H1N1-09, nasal'),
    (NEWID(), 'Lyme disease vaccine, adult dosage, for intramuscular use', '90665', '66', 'Lyme disease'),
    (NEWID(), 'Influenza virus vaccine (IIV), pandemic formulation, split virus, preservative free, for intramuscular use', '90666', '126', 'Novel influenza-H1N1-09, preservative-free'),
    (NEWID(), 'Influenza virus vaccine (IIV), pandemic formulation, split virus, for intramuscular use', '90668', '127', 'Novel influenza-H1N1-09'),
    (NEWID(), 'Pneumococcal conjugate vaccine, 7 valent, for intramuscular use', '90669', '100', 'pneumococcal conjugate PCV 7'),
    (NEWID(), 'Pneumococcal conjugate vaccine, 13 valent (PCV13), for intramuscular use', '90670', '133', 'Pneumococcal conjugate PCV 13'),
    (NEWID(), 'Pneumococcal conjugate vaccine, 15 valent (PCV15), for intramuscular use', '90671', '215', 'Pneumococcal conjugate PCV15, polysaccharide CRM197 conjugate, adjuvant, PF'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent, live (LAIV4), for intranasal use', '90672', '149', 'Influenza, live, quadrivalent, intranasal'),
    (NEWID(), 'Influenza virus vaccine, trivalent (RIV3), derived from recombinant DNA, hemagglutinin (HA) protein only, preservative and antibiotic free, for intramuscular use', '90673', '155', 'Influenza, recombinant, trivalent, PF'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (ccIIV4), derived from cell cultures, subunit, preservative and antibiotic free, 0.5 mL dosage, for intramuscular use', '90674', '171', 'Influenza, MDCK, quadrivalent, PF'),
    (NEWID(), 'Rabies vaccine, for intramuscular use', '90675', '175', 'Rabies - IM Diploid cell culture'),
    (NEWID(), 'Rabies vaccine, for intramuscular use', '90675', '176', 'Rabies - IM fibroblast culture'),
    (NEWID(), 'Rabies vaccine, for intradermal use', '90676', '40', 'rabies, intradermal injection'),
    (NEWID(), 'Pneumococcal conjugate vaccine, 20 valent (PCV20), for intramuscular use', '90677', '216', 'Pneumococcal conjugate PCV20, polysaccharide CRM197 conjugate, adjuvant, PF'),
    (NEWID(), 'Respiratory syncytial virus vaccine, preF, subunit, bivalent, for intramuscular use', '90678', '305', 'RSV, bivalent, protein subunit RSVpreF, diluent reconstituted, 0.5 mL, PF'),
    (NEWID(), 'Respiratory syncytial virus vaccine, preF, recombinant, subunit, adjuvanted, for intramuscular use', '90679', '303', 'RSV, recombinant, protein subunit RSVpreF, adjuvant reconstituted, 0.5 mL, PF'),
    (NEWID(), 'Rotavirus vaccine, pentavalent (RV5), 3 dose schedule, live, for oral use', '90680', '116', 'rotavirus, pentavalent'),
    (NEWID(), 'Rotavirus vaccine, human, attenuated (RV1), 2 dose schedule, live, for oral use', '90681', '119', 'rotavirus, monovalent'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (RIV4), derived from recombinant DNA, hemagglutinin (HA) protein only, preservative and antibiotic free, for intramuscular use', '90682', '185', 'Influenza, recombinant, quadrivalent, PF'),
    (NEWID(), 'Respiratory syncytial virus vaccine, mRNA lipid nanoparticles, for intramuscular use', '90683', '326', 'RSV, mRNA, injectable, PF'),
    (NEWID(), 'Pneumococcal conjugate vaccine, 21 valent (PCV21), for intramuscular use', '90684', '327', 'Pneumococcal conjugate PCV21, polysaccharide CRM197 conjugate, PF'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (IIV4), split virus, preservative free, 0.25 mL dosage, for intramuscular use', '90685', '161', 'Influenza, injectable,quadrivalent, preservative free, pediatric'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (IIV4), split virus, preservative free, 0.5 mL dosage, for intramuscular use', '90686', '150', 'Influenza, split virus, quadrivalent, PF'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (IIV4), split virus, 0.25 mL dosage, for intramuscular use', '90687', '158', 'Influenza, split virus, quadrivalent, preservative'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (IIV4), split virus, 0.5 mL dosage, for intramuscular use', '90688', '158', 'Influenza, split virus, quadrivalent, preservative'),
    (NEWID(), 'Typhoid vaccine, live, oral', '90690', '25', 'typhoid, oral'),
    (NEWID(), 'Typhoid vaccine, Vi capsular polysaccharide (ViCPs), for intramuscular use', '90691', '101', 'typhoid, ViCPs'),
    (NEWID(), 'Typhoid vaccine, heat- and phenol-inactivated (H-P), for subcutaneous or intradermal use', '90692', '41', 'typhoid, parenteral'),
    (NEWID(), 'Typhoid vaccine, acetone-killed, dried (AKD), for subcutaneous use (U.S. military)', '90693', '53', 'typhoid, parenteral, AKD (U.S. military)'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (aIIV4), inactivated, adjuvanted, preservative free, 0.5 mL dosage, for intramuscular use', '90694', '205', 'Influenza, adjuvanted, quadrivalent, PF'),
    (NEWID(), 'Diphtheria, tetanus toxoids, acellular pertussis vaccine and inactivated poliovirus vaccine (DTaP-IPV), when administered to children 4 through 6 years of age, for intramuscular use', '90696', '130', 'DTaP-IPV'),
    (NEWID(), 'Diphtheria, tetanus toxoids, acellular pertussis vaccine, inactivated poliovirus vaccine, Haemophilus influenzae type b PRP-OMP conjugate vaccine, and hepatitis B vaccine (DTaP-IPV-Hib-HepB), for intramuscular use', '90697', '146', 'DTaP,IPV,Hib,HepB'),
    (NEWID(), 'Diphtheria, tetanus toxoids, acellular pertussis vaccine, Haemophilus influenzae type b, and inactivated poliovirus vaccine, (DTaP-IPV/Hib), for intramuscular use', '90698', '120', 'DTaP-Hib-IPV'),
    (NEWID(), 'Diphtheria, tetanus toxoids, and acellular pertussis vaccine (DTaP), when administered to individuals younger than 7 years, for intramuscular use', '90700', '20', 'DTaP'),
    (NEWID(), 'Diphtheria, tetanus toxoids, and acellular pertussis vaccine (DTaP), when administered to individuals younger than 7 years, for intramuscular use', '90700', '106', 'DTaP, 5 pertussis antigens'),
    (NEWID(), 'Diphtheria, tetanus toxoids, and whole cell pertussis vaccine (DTP), for intramuscular use', '90701', '01', 'DTP'),
    (NEWID(), 'Diphtheria and tetanus toxoids adsorbed (DT) when administered to individuals younger than 7 years, for intramuscular use', '90702', '28', 'DT (pediatric)'),
    (NEWID(), 'Tetanus toxoid adsorbed, for intramuscular use', '90703', '35', 'tetanus toxoid, adsorbed'),
    (NEWID(), 'Mumps virus vaccine, live, for subcutaneous use', '90704', '07', 'mumps'),
    (NEWID(), 'Measles virus vaccine, live, for subcutaneous use', '90705', '05', 'measles'),
    (NEWID(), 'Rubella virus vaccine, live, for subcutaneous use', '90706', '06', 'rubella'),
    (NEWID(), 'Measles, mumps and rubella virus vaccine (MMR), live, for subcutaneous use', '90707', '03', 'MMR'),
    (NEWID(), 'Measles and rubella virus vaccine, live, for subcutaneous use', '90708', '04', 'M/R'),
    (NEWID(), 'Measles, mumps, rubella, and varicella vaccine (MMRV), live, for subcutaneous use', '90710', '94', 'MMRV'),
    (NEWID(), 'Poliovirus vaccine, (any type[s]) (OPV), live, for oral use', '90712', '182', 'OPV, Unspecified'),
    (NEWID(), 'Poliovirus vaccine, inactivated (IPV), for subcutaneous or intramuscular use', '90713', '10', 'IPV'),
    (NEWID(), 'Tetanus and diphtheria toxoids adsorbed (Td), preservative free, when administered to individuals 7 years or older, for intramuscular use', '90714', '09', 'Td (adult), 2 Lf tetanus toxoid, preservative free, adsorbed'),
    (NEWID(), 'Tetanus and diphtheria toxoids adsorbed (Td), preservative free, when administered to individuals 7 years or older, for intramuscular use', '90714', '113', 'Td (adult), 5 Lf tetanus toxoid, preservative free, adsorbed'),
    (NEWID(), 'Tetanus, diphtheria toxoids and acellular pertussis vaccine (Tdap), when administered to individuals 7 years or older, for intramuscular use', '90715', '115', 'Tdap'),
    (NEWID(), 'Varicella virus vaccine (VAR), live, for subcutaneous use', '90716', '21', 'varicella'),
    (NEWID(), 'Yellow fever vaccine, live, for subcutaneous use', '90717', '37', 'yellow fever live'),
    (NEWID(), 'Yellow fever vaccine, live, for subcutaneous use', '90717', '183', 'Yellow fever vaccine live - alt'),
    (NEWID(), 'Diphtheria, tetanus toxoids, and whole cell pertussis vaccine and Hemophilus influenza B vaccine (DTP-Hib), for intramuscular use', '90720', '22', 'DTP-Hib'),
    (NEWID(), 'Diphtheria, tetanus toxoids, and acellular pertussis vaccine and Hemophilus influenza B vaccine (DTaP/Hib), for intramuscular use', '90721', '50', 'DTaP-Hib'),
    (NEWID(), 'Diphtheria, tetanus toxoids, acellular pertussis vaccine, hepatitis B, and inactivated poliovirus vaccine (DTaP-HepB-IPV), for intramuscular use', '90723', '110', 'DTaP-Hep B-IPV'),
    (NEWID(), 'Influenza virus vaccine', '90724', '88', 'influenza, unspecified formulation'),
    (NEWID(), 'Cholera vaccine for injectable use', '90725', '26', 'cholera, unspecified formulation'),
    (NEWID(), 'Rabies vaccine', '90726', '90', 'rabies, unspecified formulation'),
    (NEWID(), 'Plague vaccine, for intramuscular use', '90727', '23', 'plague'),
    (NEWID(), 'BCG vaccine', '90728', '19', 'BCG'),
    (NEWID(), 'Hepatitis A vaccine', '90730', '85', 'Hep A, unspecified formulation'),
    (NEWID(), 'Hepatitis B vaccine', '90731', '45', 'Hep B, unspecified formulation'),
    (NEWID(), 'Pneumococcal polysaccharide vaccine, 23-valent (PPSV23), adult or immunosuppressed patient dosage, when administered to individuals 2 years or older, for subcutaneous or intramuscular use', '90732', '33', 'pneumococcal polysaccharide PPV23'),
    (NEWID(), 'Meningococcal polysaccharide vaccine, serogroups A, C, Y, W-135, quadrivalent (MPSV4), for subcutaneous use', '90733', '32', 'meningococcal MPSV4'),
    (NEWID(), 'Meningococcal conjugate vaccine, serogroups A, C, W, Y, quadrivalent, diphtheria toxoid carrier (MenACWY-D) or CRM197 carrier (MenACWY-CRM), for intramuscular use', '90734', '114', 'meningococcal MCV4P'),
    (NEWID(), 'Meningococcal conjugate vaccine, serogroups A, C, W, Y, quadrivalent, diphtheria toxoid carrier (MenACWY-D) or CRM197 carrier (MenACWY-CRM), for intramuscular use', '90734', '136', 'Meningococcal MCV4O'),
    (NEWID(), 'Japanese encephalitis virus vaccine, for subcutaneous use', '90735', '39', 'Japanese encephalitis SC'),
    (NEWID(), 'Zoster (shingles) vaccine (HZV), live, for subcutaneous injection', '90736', '121', 'zoster live'),
    (NEWID(), 'Hemophilus influenza B', '90737', '17', 'Hib, unspecified formulation'),
    (NEWID(), 'Japanese encephalitis virus vaccine, inactivated, for intramuscular use', '90738', '134', 'Japanese Encephalitis IM'),
    (NEWID(), 'Hepatitis B vaccine (HepB), CpG-adjuvanted, adult dosage, 2 dose or 4 dose schedule, for intramuscular use', '90739', '189', 'HepB-CpG'),
    (NEWID(), 'Hepatitis B vaccine (HepB), dialysis or immunosuppressed patient dosage, 3 dose schedule, for intramuscular use', '90740', '44', 'Hep B, high-dosage, dialysis or IC'),
    (NEWID(), 'Immunization, passive; immune serum globulin, human (ISG)', '90741', '14', 'IG, unspecified formulation'),
    (NEWID(), 'Hepatitis B vaccine (HepB), adolescent, 2 dose schedule, for intramuscular use', '90743', '08', 'Hep B, adolescent or pediatric'),
    (NEWID(), 'Hepatitis B vaccine (HepB), adolescent, 2 dose schedule, for intramuscular use', '90743', '43', 'Hep B, adult'),
    (NEWID(), 'Hepatitis B vaccine (HepB), pediatric/adolescent dosage, 3 dose schedule, for intramuscular use', '90744', '08', 'Hep B, adolescent or pediatric'),
    (NEWID(), 'Hepatitis B vaccine, adolescent/high risk infant dosage, for intramuscular use', '90745', '42', 'Hep B, adolescent/high risk infant'),
    (NEWID(), 'Hepatitis B vaccine (HepB), adult dosage, 3 dose schedule, for intramuscular use', '90746', '43', 'Hep B, adult'),
    (NEWID(), 'Hepatitis B vaccine (HepB), dialysis or immunosuppressed patient dosage, 4 dose schedule, for intramuscular use', '90747', '44', 'Hep B, high-dosage, dialysis or IC'),
    (NEWID(), 'Hepatitis B and Haemophilus influenzae type b vaccine (Hib-HepB), for intramuscular use', '90748', '51', 'Hib-Hep B'),
    (NEWID(), 'Zoster (shingles) vaccine (HZV), recombinant, subunit, adjuvanted, for intramuscular use', '90750', '187', 'zoster recombinant'),
    (NEWID(), 'Influenza virus vaccine, quadrivalent (ccIIV4), derived from cell cultures, subunit, antibiotic free, 0.5 mL dosage, for intramuscular use', '90756', '186', 'Influenza, MDCK, quadrivalent, preservative'),
    (NEWID(), 'Zaire ebolavirus vaccine, live, for intramuscular use', '90758', '204', 'Ebola Zaire vaccine, live, recombinant, 1mL dose'),
    (NEWID(), 'Hepatitis B vaccine (HepB), 3-antigen (S, Pre-S1, Pre-S2), 10 mcg dosage, 3 dose schedule, for intramuscular use', '90759', '220', 'HepB recombinant, 3-antigen, Al(OH)3'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, preservative free, 30 mcg/0.3 mL dosage, diluent reconstituted, for intramuscular use', '91300', '208', 'COVID-19, mRNA, LNP-S, PF, 30 mcg/0.3 mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, preservative free, 100 mcg/0.5 mL dosage, for intramuscular use', '91301', '207', 'COVID-19, mRNA, LNP-S, PF, 100 mcg/0.5mL dose or 50 mcg/0.25mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, DNA, spike protein, chimpanzee adenovirus Oxford 1 (ChAdOx1) vector, preservative free, 5x1010 viral particles/0.5 mL dosage, for intramuscular use', '91302', '210', 'COVID-19 vaccine, vector-nr, rS-ChAdOx1, PF, 0.5 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, DNA, spike protein, adenovirus type 26 (Ad26) vector, preservative free, 5x1010 viral particles/0.5 mL dosage, for intramuscular use', '91303', '212', 'COVID-19 vaccine, vector-nr, rS-Ad26, PF, 0.5 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, recombinant spike protein nanoparticle, saponin-based adjuvant, 5 mcg/0.5 mL dosage, for intramuscular use', '91304', '313', 'COVID-19, subunit, rS-nanoparticle, adjuvanted, PF, 5 mcg/0.5 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, recombinant spike protein nanoparticle, saponin-based adjuvant, 5 mcg/0.5 mL dosage, for intramuscular use', '91304', '211', 'COVID-19, subunit, rS-nanoparticle+Matrix-M1 Adjuvant, PF, 0.5 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, preservative free, 30 mcg/0.3 mL dosage, tris-sucrose formulation, for intramuscular use', '91305', '217', 'COVID-19, mRNA, LNP-S, PF, 30 mcg/0.3 mL dose, tris-sucrose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, preservative free, 50 mcg/0.25 mL dosage, for intramuscular use', '91306', '207', 'COVID-19, mRNA, LNP-S, PF, 100 mcg/0.5mL dose or 50 mcg/0.25mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, preservative free, 10 mcg/0.2 mL dosage, diluent reconstituted, tris-sucrose formulation, for intramuscular use', '91307', '218', 'COVID-19, mRNA, LNP-S, PF, 10 mcg/0.2 mL dose, tris-sucrose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, preservative free, 3 mcg/0.2 mL dosage, diluent reconstituted, tris-sucrose formulation, for intramuscular use', '91308', '219', 'COVID-19, mRNA, LNP-S, PF, 3 mcg/0.2 mL dose, tris-sucrose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, preservative free, 50 mcg/0.5 mL dosage, for intramuscular use', '91309', '221', 'COVID-19, mRNA, LNP-S, PF, 50 mcg/0.5 mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, monovalent, preservative free, 5 mcg/0.5 mL dosage, adjuvant AS03 emulsion, for intramuscular use', '91310', '225', 'COVID-19, D614, recomb, preS dTM, AS03 adjuvant add, PF, 5mcg/0.5mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, preservative free, 25 mcg/0.25 mL dosage, for intramuscular use', '91311', '228', 'COVID-19, mRNA, LNP-S, PF, pediatric 25 mcg/0.25 mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, bivalent spike protein, preservative free, 30 mcg/0.3 mL dosage, tris-sucrose formulation, for intramuscular use', '91312', '300', 'COVID-19, mRNA, LNP-S, bivalent, PF, 30 mcg/0.3 mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, bivalent, preservative free, 50 mcg/0.5 mL dosage, for intramuscular use', '91313', '229', 'COVID-19, mRNA, LNP-S, bivalent, PF, 50 mcg/0.5 mL or 25mcg/0.25 mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, bivalent, preservative free, 25 mcg/0.25 mL dosage, for intramuscular use', '91314', '229', 'COVID-19, mRNA, LNP-S, bivalent, PF, 50 mcg/0.5 mL or 25mcg/0.25 mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, bivalent spike protein, preservative free, 10 mcg/0.2 mL dosage, diluent reconstituted, tris-sucrose formulation, for intramuscular use', '91315', '301', 'COVID-19, mRNA, LNP-S, bivalent, PF, 10 mcg/0.2 mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, bivalent, preservative free, 10 mcg/0.2 mL dosage, for intramuscular use', '91316', '230', 'COVID-19, mRNA, LNP-S, bivalent, PF, 10 mcg/0.2 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, bivalent spike protein, preservative free, 3 mcg/0.2 mL dosage, diluent reconstituted, tris-sucrose formulation, for intramuscular use', '91317', '302', 'COVID-19, mRNA, LNP-S, bivalent, PF, 3 mcg/0.2 mL dose'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, 3 mcg/0.3 mL dosage, tris-sucrose formulation, for intramuscular use', '91318', '308', 'COVID-19, mRNA, LNP-S, PF, tris-sucrose, 3 mcg/0.3 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, 10 mcg/0.3 mL dosage, tris-sucrose formulation, for intramuscular use', '91319', '310', 'COVID-19, mRNA, LNP-S, PF, tris-sucrose, 10 mcg/0.3 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, spike protein, 30 mcg/0.3 mL dosage, tris-sucrose formulation, for intramuscular use', '91320', '309', 'COVID-19, mRNA, LNP-S, PF, tris-sucrose, 30 mcg/0.3 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, 25 mcg/0.25 mL dosage, for intramuscular use', '91321', '311', 'COVID-19, mRNA, LNP-S, PF, 25 mcg/0.25 mL'),
    (NEWID(), 'Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) (coronavirus disease [COVID-19]) vaccine, mRNA-LNP, 50 mcg/0.5 mL dosage, for intramuscular use', '91322', '312', 'COVID-19, mRNA, LNP-S, PF, 50 mcg/0.5 mL');


END;

--Predefined Roles list
IF NOT EXISTS (SELECT 1 FROM [AccountService].[Roleslist])
BEGIN
    INSERT INTO [AccountService].[Roleslist] (ID, Rolename)
    VALUES
        (NEWID(), 'Admin'),
        (NEWID(), 'Provider'),
        (NEWID(), 'Patient'),
        (NEWID(), 'Facility'),
        (NEWID(), 'Receptionist'),
        (NEWID(), 'Accountant');
END;

--predefined facility types
IF NOT EXISTS (SELECT 1 FROM [AccountService].[Facilitylist])
BEGIN
   INSERT INTO [AccountService].[Facilitylist] (ID, Facilityname)
   VALUES
       (NEWID(), 'Clinic'),
       (NEWID(), 'Hospital'),
       (NEWID(), 'Lab');
END;

--Predefined Visit types
IF NOT EXISTS (SELECT 1 FROM [AccountService].[PredefinedVisitType])
BEGIN
    INSERT INTO [AccountService].[PredefinedVisitType] ([ID], [VisitName], [CPTCode]) 
    VALUES 
        (NEWID(), 'Physical Visit', 99385),
        (NEWID(), 'Regular Visit', 99213),
        (NEWID(), 'Intervention Visit', 96156),
        (NEWID(), 'Operative Report Visit', 99024),
        (NEWID(), 'Vision Visit', 92004);
END;

--Visit status list
IF NOT EXISTS (SELECT 1 FROM [AccountService].[VisitStatus])
BEGIN
    INSERT INTO [AccountService].[VisitStatus] ([ID], [VisitStatus], [OrganizationId])
    VALUES 
        (NEWID(), 'Pending', @Org1),
        (NEWID(), 'Confirmed', @Org1),
        (NEWID(), 'Arrived', @Org1),
        (NEWID(), 'In Progress', @Org1),
        (NEWID(), 'ReScheduled', @Org1),
        (NEWID(), 'Completed', @Org1),
        (NEWID(), 'Cancelled', @Org1);
END;

    PRINT 'Post-deployment script executed successfully.';
END TRY
BEGIN CATCH
    PRINT 'An error occurred: ' + ERROR_MESSAGE();
END CATCH;
