@using TeyaUIModels.Model
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SaveQueryDialog> Localizer

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 500px;">
            <MudText Typo="Typo.h6" Class="mb-4">
                @Localizer["SaveQuery"]
            </MudText>

            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="_queryName"
                                  Label="@Localizer["QueryName"]"
                                  Required="true"
                                  RequiredError="@Localizer["QueryNameRequired"]"
                                  Immediate="true"
                                  MaxLength="100" />
                </MudItem>

                <MudItem xs="12">
                    <MudTextField @bind-Value="_queryDescription"
                                  Label="@Localizer["Description"]"
                                  Lines="3"
                                  MaxLength="500"
                                  Immediate="true" />
                </MudItem>

                <MudItem xs="12">
                    <MudSwitch @bind-Checked="_isShared"
                               Label="@Localizer["ShareWithOrganization"]"
                               Color="Color.Primary" />
                    <MudText Typo="Typo.caption" Class="text-muted">
                        @Localizer["ShareQueryDescription"]
                    </MudText>
                </MudItem>

                <MudItem xs="12">
                    <MudAlert Severity="Severity.Info" Class="mt-2">
                        @Localizer["QueryWillInclude", ResultCount]
                    </MudAlert>
                </MudItem>

                @if (!string.IsNullOrEmpty(_validationMessage))
                {
                    <MudItem xs="12">
                        <MudAlert Severity="Severity.Error">
                            @_validationMessage
                        </MudAlert>
                    </MudItem>
                }
            </MudGrid>
        </MudContainer>
    </DialogContent>
    
    <DialogActions>
        <MudButton OnClick="Cancel" 
                   Variant="Variant.Text">
            @Localizer["Cancel"]
        </MudButton>
        <MudButton OnClick="Save" 
                   Variant="Variant.Filled" 
                   Color="Color.Primary"
                   Disabled="@(!IsValid())">
            @Localizer["Save"]
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = default!;
    [Parameter] public PatientRegistryFilter Filter { get; set; } = new();
    [Parameter] public int ResultCount { get; set; }

    private string _queryName = string.Empty;
    private string _queryDescription = string.Empty;
    private bool _isShared = false;
    private string _validationMessage = string.Empty;

    protected override void OnInitialized()
    {
        // Generate a default name based on current date/time
        _queryName = $"Query_{DateTime.Now:yyyyMMdd_HHmmss}";
    }

    private bool IsValid()
    {
        _validationMessage = string.Empty;

        if (string.IsNullOrWhiteSpace(_queryName))
        {
            _validationMessage = Localizer["QueryNameRequired"];
            return false;
        }

        if (_queryName.Length > 100)
        {
            _validationMessage = Localizer["QueryNameTooLong"];
            return false;
        }

        if (_queryDescription?.Length > 500)
        {
            _validationMessage = Localizer["DescriptionTooLong"];
            return false;
        }

        return true;
    }

    private void Save()
    {
        if (!IsValid())
            return;

        var savedQuery = new SavedQuery
        {
            Id = Guid.NewGuid(),
            Name = _queryName.Trim(),
            Description = _queryDescription?.Trim() ?? string.Empty,
            QueryJson = System.Text.Json.JsonSerializer.Serialize(Filter),
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(), // This should come from user context
            CreatedByName = "Current User", // This should come from user context
            OrganizationId = Guid.NewGuid(), // This should come from user context
            IsShared = _isShared,
            TimesUsed = 0
        };

        MudDialog.Close(DialogResult.Ok(savedQuery));
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
