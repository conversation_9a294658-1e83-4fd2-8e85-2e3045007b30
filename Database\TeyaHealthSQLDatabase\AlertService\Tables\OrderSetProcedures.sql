﻿CREATE TABLE [AlertService].[OrderSetProcedures] (
    [Id]               UNIQUEIDENTIFIER NOT NULL,
    [CPTCode]          NVARCHAR (100)   NOT NULL,
    [OrderedBy]        NVARCHAR (255)   NOT NULL,
    [Description]      NVARCHAR (1000)  NOT NULL,
    [Notes]            NVARCHAR (1000)  NULL,
    [OrderDate]        DATETIME2 (7)    NOT NULL,
    [AssessmentData]   NVARCHAR (1000)  NULL,
    [AssessmentId]     UNIQUEIDENTIFIER NULL,
    [OrderSetId]       UNIQUEIDENTIFIER NOT NULL,
    [ChiefComplaint]   NVARCHAR (255)   NULL,
    [ChiefComplaintId] UNIQUEIDENTIFIER NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_OrderSetProcedures_OrderSet] FOREIGN KEY ([OrderSetId]) REFERENCES [AlertService].[OrderSet] ([Id]) ON DELETE CASCADE
);

