﻿@inherits LayoutComponentBase
@using Microsoft.Extensions.Localization
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject NavigationManager Navigation
@inject ILogger<Admin> Logger
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject Blazored.LocalStorage.ILocalStorageService LocalStorage

@if (!_isThemeLoaded)
{
    <div class="loading-container">
        <MudProgressCircular Indeterminate="true" Style="width: 80px; height: 80px;" />
    </div>
}
else
{
    <MudPopoverProvider />
    <MudDialogProvider />
    <MudSnackbarProvider />

    <MudLayout>
        <CascadingValue Value="this">
            <MudAppBar Elevation="1">
                <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="DrawerToggle" />
                <MudSpacer />

                <div class="search-container">
                    <MudAutocomplete T="Member"
                                     @bind-Value="_selectedMember"
                                     SearchFunc="SearchMembers"
                                     ToStringFunc="@(member => member?.UserName)"
                                     Placeholder="@Localizer["SearchUsername"]"
                                     Clearable="true"
                                     Class="search-autocomplete"
                                     MinCharacters="1"
                                     Dense="true"
                                     Adornment="Adornment.Start"
                                     AdornmentIcon="@Icons.Material.Filled.PersonSearch"
                                     OnKeyDown="HandleKeyDown"
                                     Loading="@_isSearching"
                                     Style="width: 100%;" />

                    <MudIconButton Icon="@Icons.Material.Filled.Search"
                                   Color="Color.Primary"
                                   OnClick="SearchAndNavigateAsync"
                                   Class="search-icon-button"
                                   Size="Size.Medium" />
                </div>
                <MudSpacer />
                <MudThemeProvider Theme="CurrentTheme" />

                @if (ShouldShowProductSwitcher())
                {
                    <MudMenu Icon="@Icons.Material.Filled.Apps"
                             Color="Color.Inherit"
                             Size="Size.Large"
                             Style="color: white; margin-right: 4px;"
                             AnchorOrigin="Origin.BottomCenter"
                             TransformOrigin="Origin.TopCenter">
                        <ChildContent>
                            @foreach (var product in GetAvailableProducts())
                            {
                                <MudMenuItem OnClick="@(() => HandleProductSelection(product))"
                                             Style="min-width: 150px;">
                                    <div style="display: flex; align-items: center; padding: 8px;">
                                        <MudIcon Icon="@GetProductIcon(product)" Style="margin-right: 12px;" />
                                        <div>
                                            <MudText Typo="Typo.body1">@GetProductDisplayName(product)</MudText>
                                        </div>
                                    </div>
                                </MudMenuItem>
                            }
                        </ChildContent>
                    </MudMenu>
                }

                <MudIconButton Icon="@Icons.Material.Filled.AccountCircle" Size="Size.Large" Edge="Edge.End"
                               Style="color: white;" OnClick="OpenProfileDialog" />
            </MudAppBar>

            <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2"
                       Variant="@DrawerVariant.Mini" OpenMiniOnHover="true"
                       Style="@(_drawerOpen ? "width: 240px;" : "width: 60px;")">
                <MudDrawerHeader Style="height: 64px; display: flex; align-items: center; justify-content: center;">
                    @if (_drawerOpen)
                    {
                        <MudText Typo="Typo.h5" Class="mt-1" Style="font-weight: bold;">
                            @if (!string.IsNullOrEmpty(_selectedProductName))
                            {
                                @($"{Localizer["TeyaHealth"]} - {_selectedProductName}")
                            }
                            else
                            {
                                @Localizer["TeyaHealth"]
                            }
                        </MudText>
                    }
                    else
                    {
                        <MudImage Src="Fav-Icon.png"
                                  Alt="TeyaHealth"
                                  Width="24"
                                  Height="24" />
                    }
                </MudDrawerHeader>
                <AdminNav IsDrawerOpen="_drawerOpen" />
            </MudDrawer>
            <MudMainContent>
                @Body
            </MudMainContent>
        </CascadingValue>
    </MudLayout>
}

<style>
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
    }

    .search-container {
        display: flex;
        align-items: center;
        background-color: white;
        border-radius: 8px;
        padding: 0px 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 500px;
        margin: 15px auto 16px auto;
    }
</style>