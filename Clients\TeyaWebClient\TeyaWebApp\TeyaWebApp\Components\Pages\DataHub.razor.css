.mud-data-grid-custom {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mud-data-grid-custom .mud-table-head {
    background-color: var(--mud-palette-primary-lighten);
    color: var(--mud-palette-primary-text);
    font-weight: 600;
}

.mud-data-grid-custom .mud-table-row:hover {
    background-color: var(--mud-palette-action-hover);
    transition: background-color 0.2s ease;
}

.mud-data-grid-custom .mud-table-cell {
    padding: 12px 16px;
    border-bottom: 1px solid var(--mud-palette-divider);
}

.mud-data-grid-custom .mud-button {
    text-transform: none;
    font-weight: 500;
}

.text-muted {
    color: var(--mud-palette-text-secondary);
    font-style: italic;
}

.registry-tab-content {
    min-height: 300px;
    padding: 16px;
}

.query-operations-bar {
    background: linear-gradient(135deg, var(--mud-palette-primary-lighten) 0%, var(--mud-palette-primary) 100%);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
}

.query-operations-bar .mud-button-group {
    gap: 8px;
}

.export-section {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.filter-section {
    background-color: var(--mud-palette-surface);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid var(--mud-palette-divider);
}

.results-section {
    background-color: var(--mud-palette-surface);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid var(--mud-palette-divider);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--mud-palette-divider);
}

.patient-count {
    color: var(--mud-palette-primary);
    font-weight: 600;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.tab-icon {
    margin-right: 8px;
}

.registry-tabs .mud-tabs-panels {
    padding: 16px 0;
}

.registry-tabs .mud-tab {
    min-width: 120px;
    text-transform: none;
}

.search-section {
    background: linear-gradient(135deg, var(--mud-palette-secondary-lighten) 0%, var(--mud-palette-secondary) 100%);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.search-section .mud-input {
    background-color: white;
    border-radius: 4px;
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.status-chip {
    font-size: 0.75rem;
    font-weight: 500;
}

.patient-name-link {
    color: var(--mud-palette-primary);
    text-decoration: none;
    font-weight: 500;
}

.patient-name-link:hover {
    text-decoration: underline;
    color: var(--mud-palette-primary-darken);
}

.no-results-message {
    text-align: center;
    padding: 32px;
    color: var(--mud-palette-text-secondary);
}

.no-results-message .mud-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.filter-grid {
    gap: 16px;
}

.filter-grid .mud-input-control {
    margin-bottom: 8px;
}

.quick-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.quick-filter-chip {
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-filter-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.registry-summary {
    background: linear-gradient(135deg, var(--mud-palette-info-lighten) 0%, var(--mud-palette-info) 100%);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    color: white;
}

.registry-summary .summary-item {
    text-align: center;
}

.registry-summary .summary-number {
    font-size: 2rem;
    font-weight: 700;
    display: block;
}

.registry-summary .summary-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

@media (max-width: 768px) {
    .export-section {
        justify-content: center;
        margin-top: 16px;
    }
    
    .query-operations-bar .mud-button-group {
        flex-direction: column;
        width: 100%;
    }
    
    .registry-tabs .mud-tab {
        min-width: 80px;
        font-size: 0.75rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
