/* Cosigning Component Styles - Professional Nabla-inspired Design */

.cosigning-container {
    margin: 16px 0;
    width: 100%;
}

.cosigning-paper {
    border-radius: 12px !important;
    border: 1px solid #e0e7ff;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s ease;
}

.cosigning-paper:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-1px);
}

/* Header Section */
.cosigning-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e2e8f0;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-icon {
    color: #3b82f6 !important;
}

.header-title {
    color: #1e293b !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

.status-chip {
    font-size: 0.75rem !important;
    height: 24px !important;
    font-weight: 500 !important;
}

/* Patient Information Section */
.patient-info-section {
    margin-bottom: 20px;
    padding: 12px 16px;
    background: #f1f5f9;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.patient-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.patient-info-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-label {
    color: #64748b !important;
    font-weight: 500 !important;
    min-width: 60px;
}

.info-value {
    color: #1e293b !important;
    font-weight: 600 !important;
}

/* Signature Section */
.signature-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.signature-block {
    padding: 16px;
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
    background: #ffffff;
    transition: all 0.3s ease;
}

.signature-block:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.signature-block.signed {
    border: 2px solid #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
}

.signature-block.signed:hover {
    border-color: #059669;
}

.signature-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.signature-title {
    color: #374151 !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

.signature-title.signed {
    color: #065f46 !important;
}

.signature-timestamp {
    color: #6b7280 !important;
    font-style: italic;
    margin-top: 4px;
}

.signature-actions {
    display: flex;
    justify-content: flex-start;
}

.signature-button {
    min-width: 140px !important;
    height: 40px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    text-transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

.signature-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.signature-button:disabled {
    transform: none !important;
    box-shadow: none !important;
}

.signature-divider {
    margin: 8px 0 !important;
    background-color: #e2e8f0 !important;
}

/* Lock Status */
.lock-status {
    margin-top: 16px;
}

.lock-alert {
    border-radius: 8px !important;
    background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%) !important;
    border: 1px solid #93c5fd !important;
}

.lock-content {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1e40af !important;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cosigning-paper {
        padding: 16px;
        margin: 12px 0;
    }
    
    .cosigning-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .patient-details {
        gap: 6px;
    }
    
    .patient-info-row {
        flex-wrap: wrap;
    }
    
    .signature-button {
        width: 100% !important;
        min-width: unset !important;
    }
}

@media (max-width: 480px) {
    .cosigning-container {
        margin: 8px 0;
    }
    
    .cosigning-paper {
        padding: 12px;
        border-radius: 8px !important;
    }
    
    .header-title {
        font-size: 1rem !important;
    }
    
    .patient-info-section {
        padding: 8px 12px;
    }
    
    .signature-block {
        padding: 12px;
    }
}

/* Animation for processing states */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.signature-button:disabled {
    animation: pulse 2s infinite;
}

/* Success state animations */
.signature-block.signed {
    animation: slideInSuccess 0.5s ease-out;
}

@keyframes slideInSuccess {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus states for accessibility */
.signature-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .cosigning-paper {
        border: 2px solid #000;
        background: #fff;
    }
    
    .signature-block {
        border: 2px solid #000;
    }
    
    .signature-block.signed {
        border: 2px solid #008000;
        background: #f0fff0;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .cosigning-paper,
    .signature-block,
    .signature-button {
        transition: none;
    }
    
    .cosigning-paper:hover {
        transform: none;
    }
    
    .signature-button:hover {
        transform: none;
    }
    
    .signature-block.signed {
        animation: none;
    }
}
