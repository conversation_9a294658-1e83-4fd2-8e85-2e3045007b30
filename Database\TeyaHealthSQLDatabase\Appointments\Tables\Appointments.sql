﻿CREATE TABLE [Appointments].[Appointments] (
    [Id]              UNIQUEIDENTIFIER NOT NULL,
    [UserId]          UNIQUEIDENTIFIER NOT NULL,
    [ProviderId]      UNIQUEIDENTIFIER NOT NULL,
    [FacilityId]      UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId]  UNIQUEIDENTIFIER NOT NULL,
    [PatientId]       UNIQUEIDENTIFIER NULL,
    [PatientName]     NVARCHAR (100)   NULL,
    [StartTime]       DATETIME         NOT NULL,
    [EndTime]         DATETIME         NOT NULL,
    [AppointmentDate] DATETIME         NOT NULL,
    [CreatedDate]     DATETIME         DEFAULT (getdate()) NOT NULL,
    [UpdatedDate]     DATETIME         DEFAULT (getdate()) NOT NULL,
    [Subscription]    BIT              NOT NULL,
    [Facility]        NVARCHAR (255)   NULL,
    [Provider]        NVARCHAR (255)   NULL,
    [VisitType]       NVARCHAR (100)   NULL,
    [VisitStatus]     NVARCHAR (100)   NULL,
    [Reason]          NVARCHAR (MAX)   NULL,
    [Notes]           NVARCHAR (MAX)   NULL,
    [RoomNumber]      NVARCHAR (50)    NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC)
);

