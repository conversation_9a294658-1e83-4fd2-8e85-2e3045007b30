using AlertsApi.Controllers;
using AlertsBusinessLayer;
using AlertsContracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlertsAPIControllerTests
{
    [TestFixture]
    public class CustomImmunizationAlertsControllerTests
    {
        private Mock<ICustomImmunizationAlertsCommandHandler<CustomImmunizationAlerts>> _mockCustomImmunizationAlertsCommandHandler;
        private Mock<ICustomImmunizationAlertsQueryHandler<CustomImmunizationAlerts>> _mockCustomImmunizationAlertsQueryHandler;
        private Mock<ILogger<CustomImmunizationAlertsController>> _mockLogger;
        private Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>> _mockLocalizer;
        private CustomImmunizationAlertsController _controller;

        [SetUp]
        public void Setup()
        {
            _mockCustomImmunizationAlertsCommandHandler = new Mock<ICustomImmunizationAlertsCommandHandler<CustomImmunizationAlerts>>();
            _mockCustomImmunizationAlertsQueryHandler = new Mock<ICustomImmunizationAlertsQueryHandler<CustomImmunizationAlerts>>();
            _mockLogger = new Mock<ILogger<CustomImmunizationAlertsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<AlertsApi.Resources.ControllerMessages>>();
            
            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));
            _mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()]).Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));
            
            _controller = new CustomImmunizationAlertsController(
                _mockCustomImmunizationAlertsCommandHandler.Object,
                _mockCustomImmunizationAlertsQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }
        



        [Test]
        public async Task GetAllById_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var Id = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockCustomImmunizationAlertsQueryHandler.Setup(h => h.GetCustomImmunizationAlertsById(Id)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllById(Id);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task GetAllByOrganizationIdAndIsActive_ReturnsOkResult_WithActiveAlerts()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var alerts = new List<CustomImmunizationAlerts>
            {
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "Immunization Alert 1", Description = "Description 1", IsActive = true },
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), OrganizationId = organizationId, Name = "Immunization Alert 2", Description = "Description 2", IsActive = true }
            };

            _mockCustomImmunizationAlertsQueryHandler.Setup(h => h.GetActiveCustomImmunizationAlertsByOrganizationId(organizationId)).ReturnsAsync(alerts);

            // Act
            var result = await _controller.GetAllByOrganizationIdAndIsActive(organizationId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.Value, Is.EqualTo(alerts));
        }

        [Test]
        public async Task GetAllByOrganizationIdAndIsActive_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var exceptionMessage = "Database error";
            _mockCustomImmunizationAlertsQueryHandler.Setup(h => h.GetActiveCustomImmunizationAlertsByOrganizationId(patientId)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.GetAllByOrganizationIdAndIsActive(patientId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddCustomImmunizationAlert_ReturnsOkResult_WhenAlertsAdded()
        {
            // Arrange
            var alerts = new List<CustomImmunizationAlerts>
            {
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Immunization Alert 1", Description = "Description 1" },
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Immunization Alert 2", Description = "Description 2" }
            };

            _mockCustomImmunizationAlertsCommandHandler.Setup(h => h.AddCustomImmunizationAlerts(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddCustomImmunizationAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task AddCustomImmunizationAlert_ReturnsBadRequest_WhenNoAlertsProvided()
        {
            // Act
            var result = await _controller.AddCustomImmunizationAlert(new List<CustomImmunizationAlerts>());

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task AddCustomImmunizationAlert_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alerts = new List<CustomImmunizationAlerts>
            {
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Immunization Alert 1", Description = "Description 1" }
            };

            var exceptionMessage = "Database error";
            _mockCustomImmunizationAlertsCommandHandler.Setup(h => h.AddCustomImmunizationAlerts(alerts)).ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _controller.AddCustomImmunizationAlert(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomImmunizationAlert_ReturnsOkResult_WhenAlertUpdated()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomImmunizationAlerts { Id = alertId, Name = "Updated Immunization Alert", Description = "Updated Description" };

            _mockCustomImmunizationAlertsCommandHandler.Setup(h => h.UpdateCustomImmunizationAlerts(alert)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateCustomImmunizationAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task UpdateCustomImmunizationAlert_ReturnsBadRequest_WhenAlertIdMismatch()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Updated Immunization Alert", Description = "Updated Description" };

            // Act
            var result = await _controller.UpdateCustomImmunizationAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task UpdateCustomImmunizationAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();
            var alert = new CustomImmunizationAlerts { Id = alertId, Name = "Updated Immunization Alert", Description = "Updated Description" };

            _mockCustomImmunizationAlertsCommandHandler.Setup(h => h.UpdateCustomImmunizationAlerts(alert)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateCustomImmunizationAlert(alertId, alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task UpdateCustomImmunizationAlertList_ReturnsOkResult_WhenAlertsUpdated()
        {
            // Arrange
            var alerts = new List<CustomImmunizationAlerts>
            {
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Updated Immunization Alert 1", Description = "Updated Description 1" },
                new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Updated Immunization Alert 2", Description = "Updated Description 2" }
            };

            _mockCustomImmunizationAlertsCommandHandler.Setup(h => h.UpdateCustomImmunizationAlertsList(alerts)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateCustomImmunizationAlertList(alerts);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomImmunizationAlert_ReturnsOkResult_WhenAlertDeleted()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomImmunizationAlertsCommandHandler.Setup(h => h.DeleteCustomImmunizationAlertsById(alertId)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteCustomImmunizationAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomImmunizationAlert_ReturnsNotFound_WhenAlertDoesNotExist()
        {
            // Arrange
            var alertId = Guid.NewGuid();

            _mockCustomImmunizationAlertsCommandHandler.Setup(h => h.DeleteCustomImmunizationAlertsById(alertId)).ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteCustomImmunizationAlert(alertId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteCustomImmunizationAlertByEntity_ReturnsBadRequest_WhenAlertIsNull()
        {
            // Act
            var result = await _controller.DeleteCustomImmunizationAlertByEntity(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task DeleteCustomImmunizationAlertByEntity_ReturnsOkResult_WhenDeletedSuccessfully()
        {
            // Arrange
            var alert = new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Test Alert" };

            _mockCustomImmunizationAlertsCommandHandler
                .Setup(h => h.DeleteCustomImmunizationAlertsByEntity(alert))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteCustomImmunizationAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
        }

        [Test]
        public async Task DeleteCustomImmunizationAlertByEntity_ReturnsNotFound_WhenAlertNotFound()
        {
            // Arrange
            var alert = new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Missing Alert" };

            _mockCustomImmunizationAlertsCommandHandler
                .Setup(h => h.DeleteCustomImmunizationAlertsByEntity(alert))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteCustomImmunizationAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task DeleteCustomImmunizationAlertByEntity_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var alert = new CustomImmunizationAlerts { Id = Guid.NewGuid(), Name = "Crash Alert" };

            _mockCustomImmunizationAlertsCommandHandler
                .Setup(h => h.DeleteCustomImmunizationAlertsByEntity(alert))
                .ThrowsAsync(new Exception("Something went wrong"));

            // Act
            var result = await _controller.DeleteCustomImmunizationAlertByEntity(alert);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
        }




    }
}
