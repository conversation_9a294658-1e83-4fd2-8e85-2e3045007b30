@using TeyaUIModels.Model
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<DataHubResults> Localizer

<MudDataGrid Items="@Patients"
             Filterable="true"
             SortMode="SortMode.Multiple"
             Groupable="false"
             Hover="true"
             Dense="true"
             Loading="@IsLoading"
             LoadingProgressColor="Color.Info"
             Class="mud-data-grid-custom">
    
    <Columns>
        <PropertyColumn Property="x => x.FullName" 
                        Title="@Localizer["Name"]" 
                        Sortable="true" 
                        Filterable="true">
            <CellTemplate>
                <MudButton Variant="Variant.Text" 
                           Color="Color.Primary" 
                           OnClick="@(() => OnPatientClicked(context.Item))"
                           Class="text-left">
                    @context.Item.FullName
                </MudButton>
            </CellTemplate>
        </PropertyColumn>

        <PropertyColumn Property="x => x.Email" 
                        Title="@Localizer["Email"]" 
                        Sortable="true" 
                        Filterable="true" />

        <PropertyColumn Property="x => x.PhoneNumber" 
                        Title="@Localizer["Phone"]" 
                        Sortable="true" 
                        Filterable="true" />

        <PropertyColumn Property="x => x.DateOfBirth" 
                        Title="@Localizer["DateOfBirth"]" 
                        Sortable="true" 
                        Format="yyyy-MM-dd" />

        <PropertyColumn Property="x => x.Age" 
                        Title="@Localizer["Age"]" 
                        Sortable="true">
            <CellTemplate>
                @if (context.Item.Age.HasValue)
                {
                    <span>@context.Item.Age</span>
                }
                else
                {
                    <span class="text-muted">-</span>
                }
            </CellTemplate>
        </PropertyColumn>

        <PropertyColumn Property="x => x.Gender" 
                        Title="@Localizer["Gender"]" 
                        Sortable="true" 
                        Filterable="true" />

        <PropertyColumn Property="x => x.City" 
                        Title="@Localizer["City"]" 
                        Sortable="true" 
                        Filterable="true" />

        <PropertyColumn Property="x => x.State" 
                        Title="@Localizer["State"]" 
                        Sortable="true" 
                        Filterable="true" />

        <PropertyColumn Property="x => x.PrimaryProvider" 
                        Title="@Localizer["PrimaryProvider"]" 
                        Sortable="true" 
                        Filterable="true" />

        <PropertyColumn Property="x => x.LastVisitDate" 
                        Title="@Localizer["LastVisit"]" 
                        Sortable="true" 
                        Format="yyyy-MM-dd">
            <CellTemplate>
                @if (context.Item.LastVisitDate.HasValue)
                {
                    <span>@context.Item.LastVisitDate.Value.ToString("yyyy-MM-dd")</span>
                }
                else
                {
                    <span class="text-muted">@Localizer["NoVisits"]</span>
                }
            </CellTemplate>
        </PropertyColumn>

        <PropertyColumn Property="x => x.IsActive" 
                        Title="@Localizer["Status"]" 
                        Sortable="true">
            <CellTemplate>
                @if (context.Item.IsActive)
                {
                    <MudChip Color="Color.Success" Size="Size.Small">
                        @Localizer["Active"]
                    </MudChip>
                }
                else
                {
                    <MudChip Color="Color.Error" Size="Size.Small">
                        @Localizer["Inactive"]
                    </MudChip>
                }
            </CellTemplate>
        </PropertyColumn>

        <PropertyColumn Property="x => x.CreatedAt" 
                        Title="@Localizer["CreatedAt"]" 
                        Sortable="true" 
                        Format="yyyy-MM-dd HH:mm" />

        <TemplateColumn Title="@Localizer["Actions"]" Sortable="false" Filterable="false">
            <CellTemplate>
                <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                    <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                   Color="Color.Primary"
                                   Size="Size.Small"
                                   OnClick="@(() => ViewPatientDetails(context.Item))"
                                   Title="@Localizer["ViewDetails"]" />
                    
                    <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                   Color="Color.Secondary"
                                   Size="Size.Small"
                                   OnClick="@(() => EditPatient(context.Item))"
                                   Title="@Localizer["Edit"]" />
                    
                    <MudIconButton Icon="@Icons.Material.Filled.MedicalServices"
                                   Color="Color.Info"
                                   Size="Size.Small"
                                   OnClick="@(() => ViewMedicalHistory(context.Item))"
                                   Title="@Localizer["MedicalHistory"]" />
                </MudButtonGroup>
            </CellTemplate>
        </TemplateColumn>
    </Columns>

    <PagerContent>
        <MudDataGridPager T="PatientRegistryData" />
    </PagerContent>
</MudDataGrid>

@if (Patients?.Any() != true && !IsLoading)
{
    <MudAlert Severity="Severity.Info" Class="mt-4">
        <MudText>@Localizer["NoPatientDataAvailable"]</MudText>
    </MudAlert>
}

@code {
    [Parameter] public List<PatientRegistryData>? Patients { get; set; }
    [Parameter] public EventCallback<PatientRegistryData> OnPatientSelected { get; set; }
    [Parameter] public bool IsLoading { get; set; }

    private async Task OnPatientClicked(PatientRegistryData patient)
    {
        await OnPatientSelected.InvokeAsync(patient);
    }

    private async Task ViewPatientDetails(PatientRegistryData patient)
    {
        // Navigate to patient details page or show details dialog
        await OnPatientSelected.InvokeAsync(patient);
    }

    private async Task EditPatient(PatientRegistryData patient)
    {
        // Navigate to patient edit page or show edit dialog
        await OnPatientSelected.InvokeAsync(patient);
    }

    private async Task ViewMedicalHistory(PatientRegistryData patient)
    {
        // Navigate to medical history page or show medical history dialog
        await OnPatientSelected.InvokeAsync(patient);
    }
}
